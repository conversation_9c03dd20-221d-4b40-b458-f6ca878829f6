using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 客户折扣方案
    /// </summary>
    public class CustomerDiscountSolution : BaseEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public virtual Customer Customer { get; set; } = null!;

        /// <summary>
        /// 折扣方案名称
        /// </summary>
        public string DiscountSolutionName { get; set; } = string.Empty;

        /// <summary>
        /// 折扣方案编码
        /// </summary>
        public string? DiscountSolutionCode { get; set; }

        /// <summary>
        /// 折扣类型
        /// </summary>
        public string? DiscountType { get; set; }

        /// <summary>
        /// 折扣比例
        /// </summary>
        public decimal? DiscountRate { get; set; }

        /// <summary>
        /// 折扣金额
        /// </summary>
        public decimal? DiscountAmount { get; set; }

        /// <summary>
        /// 适用条件
        /// </summary>
        public string? ApplicableConditions { get; set; }

        /// <summary>
        /// 生效时间
        /// </summary>
        public DateTime? EffectiveTime { get; set; }

        /// <summary>
        /// 失效时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 方案状态
        /// </summary>
        public string? SolutionStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
} 
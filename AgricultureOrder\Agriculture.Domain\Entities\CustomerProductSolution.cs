using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 客户产品方案
    /// </summary>
    public class CustomerProductSolution : BaseEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public virtual Customer Customer { get; set; } = null!;

        /// <summary>
        /// 方案名称
        /// </summary>
        public string SolutionName { get; set; } = string.Empty;

        /// <summary>
        /// 方案编码
        /// </summary>
        public string? SolutionCode { get; set; }

        /// <summary>
        /// 方案描述
        /// </summary>
        public string? SolutionDescription { get; set; }

        /// <summary>
        /// 方案类型
        /// </summary>
        public string? SolutionType { get; set; }

        /// <summary>
        /// 方案状态
        /// </summary>
        public string? SolutionStatus { get; set; }

        /// <summary>
        /// 生效时间
        /// </summary>
        public DateTime? EffectiveTime { get; set; }

        /// <summary>
        /// 失效时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
} 
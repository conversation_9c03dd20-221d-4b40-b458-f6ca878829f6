<template>
  <el-dialog
    v-model="visible"
    title="地图定位"
    width="90%"
    :before-close="handleClose"
    class="map-location-dialog"
    top="5vh"
  >
    <div class="dialog-content">
      <!-- 地址输入区域 -->
      <div class="address-input-section">
        <el-form :model="addressForm" label-width="80px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="省份">
                <el-select v-model="addressForm.province" placeholder="请选择省份" style="width: 100%">
                  <el-option label="北京市" value="北京市" />
                  <el-option label="上海市" value="上海市" />
                  <el-option label="广东省" value="广东省" />
                  <el-option label="江苏省" value="江苏省" />
                  <el-option label="浙江省" value="浙江省" />
                  <el-option label="山东省" value="山东省" />
                  <el-option label="河南省" value="河南省" />
                  <el-option label="四川省" value="四川省" />
                  <el-option label="湖北省" value="湖北省" />
                  <el-option label="湖南省" value="湖南省" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="详细地址">
                <el-input 
                  v-model="addressForm.detail" 
                  placeholder="请输入详细地址"
                  @keyup.enter="searchAddress"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-button type="primary" @click="searchAddress" :loading="searching">
                  搜索
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      
      <!-- 地图区域 -->
      <div class="map-section">
        <BaiduMap
          ref="mapRef"
          :initial-location="initialLocation"
          @location-selected="handleLocationSelected"
          @location-cleared="handleLocationCleared"
        />
      </div>
      
      <!-- 位置信息显示 -->
      <div v-if="selectedLocation" class="location-display">
        <el-card class="location-card">
          <template #header>
            <div class="card-header">
              <el-icon color="#409EFF"><Location /></el-icon>
              <span>已选择位置</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="地址">
              {{ selectedLocation.address }}
            </el-descriptions-item>
            <el-descriptions-item label="经度">
              {{ selectedLocation.lng.toFixed(6) }}
            </el-descriptions-item>
            <el-descriptions-item label="纬度">
              {{ selectedLocation.lat.toFixed(6) }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag type="success">已定位</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmLocation" :disabled="!selectedLocation">
          确认位置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Location } from '@element-plus/icons-vue'
import { mapService } from '@/services/mapService'
import BaiduMap from './BaiduMap.vue'

// 定义props
interface Props {
  modelValue: boolean
  initialAddress?: string
  initialProvince?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  initialAddress: '',
  initialProvince: ''
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  locationSelected: [location: { lng: number; lat: number; address: string }]
}>()

// 响应式数据
const mapRef = ref()
const searching = ref(false)
const selectedLocation = ref<{ lng: number; lat: number; address: string } | null>(null)

// 地址表单
const addressForm = reactive({
  province: props.initialProvince || '',
  detail: props.initialAddress || ''
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 初始位置
const initialLocation = computed(() => {
  if (selectedLocation.value) {
    return selectedLocation.value
  }
  
  // 如果有初始地址，尝试获取坐标
  const fullAddress = `${addressForm.province} ${addressForm.detail}`.trim()
  if (fullAddress) {
    return {
      lng: 116.397428,
      lat: 39.90923,
      address: fullAddress
    }
  }
  
  return {
    lng: 116.397428,
    lat: 39.90923,
    address: '北京市'
  }
})

// 搜索地址
const searchAddress = async () => {
  const fullAddress = `${addressForm.province} ${addressForm.detail}`.trim()
  
  if (!fullAddress) {
    ElMessage.warning('请输入地址信息')
    return
  }
  
  searching.value = true
  try {
    console.log('开始搜索地址:', fullAddress)
    
    const location = await mapService.searchAddress(fullAddress)
    
    if (location) {
      console.log('搜索成功，设置位置:', location)
      selectedLocation.value = location
      
      // 更新地图位置
      if (mapRef.value) {
        mapRef.value.setLocation(location.lng, location.lat, location.address)
      }
      
      ElMessage.success(`地址搜索成功: ${location.address}`)
    } else {
      ElMessage.warning('未找到该地址，请尝试其他关键词')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searching.value = false
  }
}

// 处理位置选择
const handleLocationSelected = (location: { lng: number; lat: number; address: string }) => {
  selectedLocation.value = location
  
  // 解析地址信息，更新表单
  const addressParts = location.address.split(' ')
  if (addressParts.length >= 2) {
    addressForm.province = addressParts[0]
    addressForm.detail = addressParts.slice(1).join(' ')
  } else {
    addressForm.detail = location.address
  }
  
  ElMessage.success('位置已选择')
}

// 处理位置清除
const handleLocationCleared = () => {
  selectedLocation.value = null
  ElMessage.info('位置已清除')
}

// 确认位置
const confirmLocation = () => {
  if (selectedLocation.value) {
    emit('locationSelected', selectedLocation.value)
    visible.value = false
    ElMessage.success('位置已确认')
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 监听初始地址变化
watch(() => props.initialAddress, (newAddress) => {
  if (newAddress) {
    addressForm.detail = newAddress
  }
})

watch(() => props.initialProvince, (newProvince) => {
  if (newProvince) {
    addressForm.province = newProvince
  }
})
</script>

<style scoped>
.map-location-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.address-input-section {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.map-section {
  height: 500px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
}

.location-display {
  margin-top: 10px;
}

.location-card {
  border: 1px solid #b3d8ff;
  background: #f0f9ff;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #409EFF;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .map-location-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 2.5vh auto;
    }
  }
  
  .map-section {
    height: 400px;
  }
  
  .address-input-section {
    padding: 15px;
  }
}
</style> 
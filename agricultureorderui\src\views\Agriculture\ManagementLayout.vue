<template>
  <div class="management-layout">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <h3 v-if="!sidebarCollapsed">业务管理</h3>
        <el-icon v-else><Setting /></el-icon>
        <el-button
          type="text"
          @click="toggleSidebar"
          class="collapse-btn"
        >
          <el-icon>
            <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
          </el-icon>
        </el-button>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        :collapse="sidebarCollapsed"
        router
      >
        <el-menu-item index="/supplier">
          <el-icon><User /></el-icon>
          <template #title>供应商管理</template>
        </el-menu-item>
        
        <el-menu-item index="/customer">
          <el-icon><UserFilled /></el-icon>
          <template #title>客户管理</template>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ expanded: sidebarCollapsed }">
      <!-- 顶部导航栏 -->
      <div class="top-navbar">
        <div class="breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>业务管理</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="navbar-actions">
          <el-button
            type="text"
            @click="goHome"
            class="home-btn"
          >
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
        </div>
      </div>

      <!-- 路由视图 -->
      <div class="content-area">
        <QuickNav />
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Setting, Expand, Fold, User, UserFilled, House } from '@element-plus/icons-vue'
import QuickNav from '@/components/QuickNav.vue'

const route = useRoute()
const router = useRouter()

// 侧边栏状态
const sidebarCollapsed = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 当前页面标题
const currentPageTitle = computed(() => {
  const menuMap: Record<string, string> = {
    '/supplier': '供应商管理',
    '/customer': '客户管理'
  }
  return menuMap[route.path] || '业务管理'
})

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 返回首页
const goHome = () => {
  router.push('/')
}

// 监听路由变化，自动展开侧边栏（在移动端）
watch(() => route.path, () => {
  if (window.innerWidth < 768) {
    sidebarCollapsed.value = true
  }
})
</script>

<style scoped>
.management-layout {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;
}

.sidebar {
  width: 240px;
  background: white;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid #e4e7ed;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.collapse-btn {
  color: white;
  padding: 4px;
}

.collapse-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
  flex: 1;
  border: none;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  margin: 4px 8px;
  border-radius: 6px;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #f0f2f5;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
}

.main-content.expanded {
  margin-left: 0;
}

.top-navbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.breadcrumb {
  flex: 1;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.home-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #606266;
}

.home-btn:hover {
  color: #409eff;
}

.content-area {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.collapsed {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .top-navbar {
    padding: 0 16px;
  }

  .content-area {
    padding: 16px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .sidebar {
    background: #1a1a1a;
    border-right-color: #333;
  }

  .sidebar-header {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  }

  .top-navbar {
    background: #1a1a1a;
    border-bottom-color: #333;
  }

  .management-layout {
    background-color: #0f0f0f;
  }
}
</style> 
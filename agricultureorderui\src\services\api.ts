// 导入API配置
import { getApiBaseUrl } from './apiConfig'

// 通用响应类型 - 适配后端返回格式
interface ApiResponse<T = any> {
  code: number
  message: string
  token?: string | null
  refreshToken?: string | null
  data: T
}

// 分页响应类型 - 适配后端返回格式
interface PagedResponse<T> {
  PageData: T[]
  TotalCount: number
  PageCount: number
}

// 银行账户类型
export interface BankAccount {
  id: number
  bankName: string
  accountNumber: string
  accountName: string
  branchName?: string
  isDefault: boolean
  supplierId?: number
  customerId?: number
}

// 联系信息类型
export interface Contact {
  id: number
  contactName: string
  contactPhone?: string
  contactEmail?: string
  contactAddress?: string
  position?: string
  isPrimary: boolean
  supplierId?: number
  customerId?: number
}

// 服务人员类型
export interface ServicePerson {
  id: number
  affiliatedMarket?: string
  salespersonName?: string
  salespersonId?: number
  supplierId?: number
  customerId?: number
}

// 供应商相关类型
export interface Supplier {
  id: number
  supplierNumber: string
  supplierName: string
  supplierType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  usageStatus: boolean
  entryUnit?: string
  usageUnit?: string
  remarks?: string
  isArchived: boolean
  longitude?: number
  latitude?: number
  createTime: string
  updateTime: string
  // 关联数据
  bankAccounts?: BankAccount[]
  contacts?: Contact[]
  servicePersons?: ServicePerson[]
  productPrices?: any[]
  attachments?: any[]
  associatedAccount?: any
}

// 客户相关类型
export interface Customer {
  id: number
  customerNumber: string
  customerName: string
  customerType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  customerStage?: string
  customerLevel?: string
  customerGroupClassification?: string
  usageStatus: string
  entryUnit?: string
  usageUnit?: string
  superiorCustomerId?: number
  superiorCustomer?: Customer
  customTags?: string
  remarks?: string
  isArchived: boolean
  longitude?: number
  latitude?: number
  createTime: string
  updateTime: string
  // 关联数据
  bankAccounts?: any[]
  contacts?: any[]
  servicePersons?: any[]
  productSolutions?: any[]
  discountSolutions?: any[]
  attachments?: any[]
  associatedAccount?: any
}

// 查询参数类型
export interface SupplierQueryParams {
  supplierNumber?: string
  supplierName?: string
  supplierType?: string
  usageStatus?: boolean | null
  pageIndex: number
  pageSize: number
}

export interface CustomerQueryParams {
  customerNumber?: string
  customerName?: string
  customerType?: string
  customerLevel?: string
  usageStatus?: string
  pageIndex: number
  pageSize: number
}

// 添加供应商参数
export interface AddSupplierParams {
  supplierNumber: string
  supplierName: string
  supplierType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  usageStatus: boolean
  entryUnit?: string
  usageUnit?: string
  remarks?: string
  isArchived: boolean
  longitude?: number | null
  latitude?: number | null
}

// 添加客户参数
export interface AddCustomerParams {
  customerNumber: string
  customerName: string
  customerType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  customerStage?: string
  customerLevel: string
  customerGroupClassification?: string
  usageStatus: string
  entryUnit?: string
  usageUnit?: string
  superiorCustomerId?: number | null
  customTags?: string
  remarks?: string
  isArchived: boolean
  longitude?: number | null
  latitude?: number | null
}

// 通用HTTP请求方法
async function request<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  }

  // 根据请求方法获取对应的API基础URL
  const method = options.method || 'GET'
  const API_BASE_URL = getApiBaseUrl(method)
  
  const fullUrl = `${API_BASE_URL}${url}`
  console.log(`发送${method}请求到: ${fullUrl}`)

  const response = await fetch(fullUrl, {
    ...defaultOptions,
    ...options,
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const result = await response.json()
  
  // 适配后端返回的数据结构
  return {
    code: result.code,
    message: result.message,
    token: result.token,
    refreshToken: result.refreshToken,
    data: result.data
  }
}

// 供应商API
export const supplierApi = {
  // 获取供应商列表
  getList: (params: SupplierQueryParams): Promise<ApiResponse<PagedResponse<Supplier>>> => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        // 将前端的小写参数名转换为后端期望的大写参数名
        const backendKey = key === 'supplierNumber' ? 'SupplierNumber' :
                          key === 'supplierName' ? 'SupplierName' :
                          key === 'supplierType' ? 'SupplierType' :
                          key === 'usageStatus' ? 'UsageStatus' :
                          key === 'pageIndex' ? 'PageIndex' :
                          key === 'pageSize' ? 'PageSize' : key
        searchParams.append(backendKey, String(value))
      }
    })
    return request(`/api/Management/GetSupplier?${searchParams.toString()}`)
  },

  // 获取供应商详情
  getDetail: (id: number): Promise<ApiResponse<Supplier>> => {
    return request(`/api/Management/GetSupplierDetail?id=${id}`)
  },

  // 添加供应商
  add: (data: AddSupplierParams): Promise<ApiResponse<Supplier>> => {
    return request('/api/Management/AddSupplier', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },
}

// 客户API
export const customerApi = {
  // 获取客户列表
  getList: (params: CustomerQueryParams): Promise<ApiResponse<PagedResponse<Customer>>> => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        // 将前端的小写参数名转换为后端期望的大写参数名
        const backendKey = key === 'customerNumber' ? 'CustomerNumber' :
                          key === 'customerName' ? 'CustomerName' :
                          key === 'customerType' ? 'CustomerType' :
                          key === 'customerLevel' ? 'CustomerLevel' :
                          key === 'usageStatus' ? 'UsageStatus' :
                          key === 'pageIndex' ? 'PageIndex' :
                          key === 'pageSize' ? 'PageSize' : key
        searchParams.append(backendKey, String(value))
      }
    })
    return request(`/api/Management/GetCustomer?${searchParams.toString()}`)
  },

  // 获取客户详情
  getDetail: (id: number): Promise<ApiResponse<Customer>> => {
    return request(`/api/Management/GetCustomerDetail?id=${id}`)
  },

  // 添加客户
  add: (data: AddCustomerParams): Promise<ApiResponse<Customer>> => {
    return request('/api/Management/AddCustomer', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },
}

// 导出默认API对象
export default {
  supplier: supplierApi,
  customer: customerApi,
} 
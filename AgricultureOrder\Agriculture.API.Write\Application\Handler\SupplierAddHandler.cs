using Agriculture.API.Write.Application.command;
using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using Agriculture.Infrastuctrue;
using MediatR;

namespace Agriculture.API.Write.Application.Handler
{
    /// <summary>
    /// 供应商添加处理器
    /// </summary>
    public class SupplierAddHandler : IRequestHandler<SupplierAddCommand, APIResult<Supplier>>
    {
        private readonly IBaseRepository<Supplier> _supplierRepository;

        public SupplierAddHandler(IBaseRepository<Supplier> supplierRepository)
        {
            _supplierRepository = supplierRepository;
        }

        public async Task<APIResult<Supplier>> Handle(SupplierAddCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<Supplier>();

            try
            {
                // 验证必填字段
                if (string.IsNullOrWhiteSpace(request.SupplierName))
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "供应商名称不能为空";
                    return result;
                }

                if (string.IsNullOrWhiteSpace(request.SupplierNumber))
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "供应商编号不能为空";
                    return result;
                }

                // 检查供应商编号是否已存在
                var existingSupplier = _supplierRepository.GetAll()
                    .FirstOrDefault(s => s.SupplierNumber == request.SupplierNumber);
                
                if (existingSupplier != null)
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "供应商编号已存在，请使用其他编号";
                    return result;
                }

                // 创建新的供应商实体
                var supplier = new Supplier
                {
                    SupplierNumber = request.SupplierNumber,
                    SupplierName = request.SupplierName,
                    SupplierType = request.SupplierType,
                    PersonInChargeName = request.PersonInChargeName,
                    PersonInChargePhone = request.PersonInChargePhone,
                    IdCard = request.IdCard,
                    Location = request.Location,
                    Industry = request.Industry,
                    UnifiedSocialCreditCode = request.UnifiedSocialCreditCode,
                    MnemonicCode = request.MnemonicCode,
                    UsageStatus = request.UsageStatus,
                    EntryUnit = request.EntryUnit,
                    UsageUnit = request.UsageUnit,
                    Remarks = request.Remarks,
                    IsArchived = request.IsArchived,
                    Longitude = request.Longitude,
                    Latitude = request.Latitude,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                // 保存到数据库
                await _supplierRepository.AddAsync(supplier);

                result.Code = ResultCode.Success;
                result.Message = "供应商添加成功";
                result.Data = supplier;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"添加供应商失败：{ex.Message}";
            }

            return result;
        }
    }
} 
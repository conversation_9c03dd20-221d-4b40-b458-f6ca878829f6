﻿using Microsoft.EntityFrameworkCore;
using Agriculture.Domain.Entities;

namespace Agriculture.Infrastuctrue
{
    public class MyDbcontext : DbContext
    {
        public MyDbcontext(DbContextOptions<MyDbcontext> options) : base(options)
        {
        }

        // 客户相关实体
        public DbSet<Customer> Customers { get; set; }
        public DbSet<CustomerAccount> CustomerAccounts { get; set; }
        public DbSet<CustomerAttachment> CustomerAttachments { get; set; }
        public DbSet<CustomerDiscountSolution> CustomerDiscountSolutions { get; set; }
        public DbSet<CustomerProductSolution> CustomerProductSolutions { get; set; }
        public DbSet<CustomerContact> CustomerContacts { get; set; }
        public DbSet<CustomerServicePerson> CustomerServicePersons { get; set; }
        public DbSet<CustomerBankAccount> CustomerBankAccounts { get; set; }

        // 供应商相关实体
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<SupplierAccount> SupplierAccounts { get; set; }
        public DbSet<SupplierAttachment> SupplierAttachments { get; set; }
        public DbSet<SupplierProductPrice> SupplierProductPrices { get; set; }
        public DbSet<SupplierContact> SupplierContacts { get; set; }
        public DbSet<SupplierServicePerson> SupplierServicePersons { get; set; }
        public DbSet<SupplierBankAccount> SupplierBankAccounts { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置供应商与银行账户的关系
            modelBuilder.Entity<Supplier>()
                .HasMany(s => s.BankAccounts)
                .WithOne(sba => sba.Supplier)
                .HasForeignKey(sba => sba.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);

            // 配置供应商与联系信息的关系
            modelBuilder.Entity<Supplier>()
                .HasMany(s => s.Contacts)
                .WithOne(sc => sc.Supplier)
                .HasForeignKey(sc => sc.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);

            // 配置供应商与服务人员的关系
            modelBuilder.Entity<Supplier>()
                .HasMany(s => s.ServicePersons)
                .WithOne(ssp => ssp.Supplier)
                .HasForeignKey(ssp => ssp.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);

            // 配置供应商与产品价格的关系
            modelBuilder.Entity<Supplier>()
                .HasMany(s => s.ProductPrices)
                .WithOne(spp => spp.Supplier)
                .HasForeignKey(spp => spp.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);

            // 配置供应商与附件的关系
            modelBuilder.Entity<Supplier>()
                .HasMany(s => s.Attachments)
                .WithOne(sa => sa.Supplier)
                .HasForeignKey(sa => sa.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);

            // 配置供应商与关联账户的关系
            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.AssociatedAccount)
                .WithOne(sa => sa.Supplier)
                .HasForeignKey<SupplierAccount>(sa => sa.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

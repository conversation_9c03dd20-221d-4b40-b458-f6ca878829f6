using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 客户联系信息
    /// </summary>
    public class CustomerContact : BaseEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public virtual Customer Customer { get; set; } = null!;

        /// <summary>
        /// 联系人姓名
        /// </summary>
        public string ContactName { get; set; } = string.Empty;

        /// <summary>
        /// 联系电话
        /// </summary>
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 联系邮箱
        /// </summary>
        public string? ContactEmail { get; set; }

        /// <summary>
        /// 联系地址
        /// </summary>
        public string? ContactAddress { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        public string? Position { get; set; }

        /// <summary>
        /// 是否主要联系人
        /// </summary>
        public bool IsPrimary { get; set; } = false;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
} 
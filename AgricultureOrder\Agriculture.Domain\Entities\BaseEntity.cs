using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 基础实体类
    /// </summary>
    public abstract class BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 创建人ID
        /// </summary>
        public long? CreateUserId { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public long? UpdateUserId { get; set; }
    }
} 
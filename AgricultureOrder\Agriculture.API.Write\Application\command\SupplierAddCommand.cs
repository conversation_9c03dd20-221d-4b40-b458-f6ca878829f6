using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using MediatR;

namespace Agriculture.API.Write.Application.command
{
    /// <summary>
    /// 供应商添加命令
    /// </summary>
    public class SupplierAddCommand : IRequest<APIResult<Supplier>>
    {
        /// <summary>
        /// 供应商编号
        /// </summary>
        public string SupplierNumber { get; set; } = string.Empty;

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; } = string.Empty;

        /// <summary>
        /// 供应商类型（个人/企业）
        /// </summary>
        public string SupplierType { get; set; } = string.Empty;

        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string? PersonInChargeName { get; set; }

        /// <summary>
        /// 负责人手机号
        /// </summary>
        public string? PersonInChargePhone { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        public string? IdCard { get; set; }

        /// <summary>
        /// 所在地
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// 所属行业
        /// </summary>
        public string? Industry { get; set; }

        /// <summary>
        /// 统一社会信用代码（税号）
        /// </summary>
        public string? UnifiedSocialCreditCode { get; set; }

        /// <summary>
        /// 助记码
        /// </summary>
        public string? MnemonicCode { get; set; }

        /// <summary>
        /// 使用状态（启用/停用）
        /// </summary>
        public bool UsageStatus { get; set; } = false;

        /// <summary>
        /// 录入单位
        /// </summary>
        public string? EntryUnit { get; set; }

        /// <summary>
        /// 使用单位
        /// </summary>
        public string? UsageUnit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 归集档案
        /// </summary>
        public bool IsArchived { get; set; } = false;

        /// <summary>
        /// 地图定位经度
        /// </summary>
        public decimal? Longitude { get; set; }

        /// <summary>
        /// 地图定位纬度
        /// </summary>
        public decimal? Latitude { get; set; }
    }
} 
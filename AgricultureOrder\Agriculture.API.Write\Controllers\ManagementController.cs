﻿using Agriculture.API.Write.Application.command;
using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Agriculture.API.Write.Controllers
{
    /// <summary>
    /// 管理控制器
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ManagementController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ManagementController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 添加供应商
        /// </summary>
        /// <param name="command">供应商添加命令</param>
        /// <returns>添加结果</returns>
        [HttpPost]
        public async Task<APIResult<Supplier>> AddSupplier([FromBody] SupplierAddCommand command)
        {
            return await _mediator.Send(command);
        }

        /// <summary>
        /// 添加客户
        /// </summary>
        /// <param name="command">客户添加命令</param>
        /// <returns>添加结果</returns>
        [HttpPost]
        public async Task<APIResult<Customer>> AddCustomer([FromBody] CustomerAddCommand command)
        {
            return await _mediator.Send(command);
        }
    }
}

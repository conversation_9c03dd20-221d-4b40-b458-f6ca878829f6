-- 检查数据库中的数据
USE AgricultureOrder;

-- 检查供应商基本信息
SELECT 'Suppliers' as TableName, COUNT(*) as RecordCount FROM Suppliers;

-- 检查供应商银行账户
SELECT 'SupplierBankAccounts' as TableName, COUNT(*) as RecordCount FROM SupplierBankAccounts;

-- 检查供应商联系信息
SELECT 'SupplierContacts' as TableName, COUNT(*) as RecordCount FROM SupplierContacts;

-- 检查供应商服务人员
SELECT 'SupplierServicePersons' as TableName, COUNT(*) as RecordCount FROM SupplierServicePersons;

-- 检查供应商产品价格
SELECT 'SupplierProductPrices' as TableName, COUNT(*) as RecordCount FROM SupplierProductPrices;

-- 检查供应商附件
SELECT 'SupplierAttachments' as TableName, COUNT(*) as RecordCount FROM SupplierAttachments;

-- 检查特定供应商的关联数据
SELECT 
    s.Id,
    s.SupplierName,
    s.SupplierNumber,
    COUNT(DISTINCT sba.Id) as BankAccountCount,
    COUNT(DISTINCT sc.Id) as ContactCount,
    COUNT(DISTINCT ssp.Id) as ServicePersonCount,
    COUNT(DISTINCT spp.Id) as ProductPriceCount,
    COUNT(DISTINCT sa.Id) as AttachmentCount
FROM Suppliers s
LEFT JOIN SupplierBankAccounts sba ON s.Id = sba.SupplierId
LEFT JOIN SupplierContacts sc ON s.Id = sc.SupplierId
LEFT JOIN SupplierServicePersons ssp ON s.Id = ssp.SupplierId
LEFT JOIN SupplierProductPrices spp ON s.Id = spp.SupplierId
LEFT JOIN SupplierAttachments sa ON s.Id = sa.SupplierId
WHERE s.Id = 5
GROUP BY s.Id, s.SupplierName, s.SupplierNumber;

-- 检查所有供应商的关联数据
SELECT 
    s.Id,
    s.SupplierName,
    s.SupplierNumber,
    COUNT(DISTINCT sba.Id) as BankAccountCount,
    COUNT(DISTINCT sc.Id) as ContactCount,
    COUNT(DISTINCT ssp.Id) as ServicePersonCount,
    COUNT(DISTINCT spp.Id) as ProductPriceCount,
    COUNT(DISTINCT sa.Id) as AttachmentCount
FROM Suppliers s
LEFT JOIN SupplierBankAccounts sba ON s.Id = sba.SupplierId
LEFT JOIN SupplierContacts sc ON s.Id = sc.SupplierId
LEFT JOIN SupplierServicePersons ssp ON s.Id = ssp.SupplierId
LEFT JOIN SupplierProductPrices spp ON s.Id = spp.SupplierId
LEFT JOIN SupplierAttachments sa ON s.Id = sa.SupplierId
GROUP BY s.Id, s.SupplierName, s.SupplierNumber
ORDER BY s.Id; 
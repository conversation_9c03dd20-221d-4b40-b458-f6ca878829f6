// API配置文件 - 处理读写API的不同端口

// API基础URL配置
export const API_CONFIG = {
  // 写API (POST, PUT, DELETE)
  WRITE_API_BASE_URL: import.meta.env.VITE_API_WRITE_BASE_URL || 'http://localhost:5214',
  
  // 读API (GET)
  READ_API_BASE_URL: import.meta.env.VITE_API_READ_BASE_URL || 'http://localhost:5102'
};

/**
 * 根据请求方法获取对应的API基础URL
 * @param method 请求方法
 * @returns 对应的API基础URL
 */
export function getApiBaseUrl(method: string): string {
  // 将方法转为大写以便比较
  const upperMethod = method.toUpperCase();
  
  // 如果是GET请求，使用读API的URL
  if (upperMethod === 'GET') {
    return API_CONFIG.READ_API_BASE_URL;
  }
  
  // 其他请求（POST, PUT, DELETE等）使用写API的URL
  return API_CONFIG.WRITE_API_BASE_URL;
}

export default API_CONFIG; 
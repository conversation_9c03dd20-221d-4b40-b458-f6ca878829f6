# 数据库设置脚本
Write-Host "开始设置数据库..." -ForegroundColor Green

# 设置工作目录
Set-Location $PSScriptRoot

# 1. 执行数据库迁移
Write-Host "执行数据库迁移..." -ForegroundColor Yellow
dotnet ef database update --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Read

if ($LASTEXITCODE -eq 0) {
    Write-Host "数据库迁移成功!" -ForegroundColor Green
} else {
    Write-Host "数据库迁移失败!" -ForegroundColor Red
    exit 1
}

# 2. 插入测试数据
Write-Host "插入测试数据..." -ForegroundColor Yellow

# 读取SQL文件内容
$sqlContent = Get-Content "insert-test-data.sql" -Raw

# 连接到MySQL并执行SQL
$connectionString = "Server=*************;uid=root;pwd=***********;database=AgricultureOrder"
$connection = New-Object System.Data.Odbc.OdbcConnection($connectionString)

try {
    $connection.Open()
    $command = $connection.CreateCommand()
    $command.CommandText = $sqlContent
    $command.ExecuteNonQuery()
    Write-Host "测试数据插入成功!" -ForegroundColor Green
} catch {
    Write-Host "插入测试数据失败: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($connection.State -eq "Open") {
        $connection.Close()
    }
}

Write-Host "数据库设置完成!" -ForegroundColor Green 
{"Version": 1, "WorkspaceRootPath": "D:\\实训一\\项目\\农业\\AgricultureOrder\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.api.read\\application\\handler\\supplierqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|solutionrelative:agriculture.api.read\\application\\handler\\supplierqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.api.read\\controllers\\managementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|solutionrelative:agriculture.api.read\\controllers\\managementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{5EB5D63E-1BBA-44AD-8343-805CCAF56301}|Agriculture.Domain\\Agriculture.Domain.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.domain\\entities\\supplier.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{5EB5D63E-1BBA-44AD-8343-805CCAF56301}|Agriculture.Domain\\Agriculture.Domain.csproj|solutionrelative:agriculture.domain\\entities\\supplier.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{5C1820D2-1768-4BD6-A6CD-4955DE48E34E}|Agriculture.Infrastuctrue\\Agriculture.Infrastuctrue.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.infrastuctrue\\mydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{5C1820D2-1768-4BD6-A6CD-4955DE48E34E}|Agriculture.Infrastuctrue\\Agriculture.Infrastuctrue.csproj|solutionrelative:agriculture.infrastuctrue\\mydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.api.read\\application\\command\\supplierquerycommand.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|solutionrelative:agriculture.api.read\\application\\command\\supplierquerycommand.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{5EB5D63E-1BBA-44AD-8343-805CCAF56301}|Agriculture.Domain\\Agriculture.Domain.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.domain\\entities\\supplierproductprice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{5EB5D63E-1BBA-44AD-8343-805CCAF56301}|Agriculture.Domain\\Agriculture.Domain.csproj|solutionrelative:agriculture.domain\\entities\\supplierproductprice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7B851BAA-238C-4D62-B6A4-7928BDD32A06}|Agriculture.API.Write\\Agriculture.API.Write.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.api.write\\application\\handler\\supplieraddhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7B851BAA-238C-4D62-B6A4-7928BDD32A06}|Agriculture.API.Write\\Agriculture.API.Write.csproj|solutionrelative:agriculture.api.write\\application\\handler\\supplieraddhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7B851BAA-238C-4D62-B6A4-7928BDD32A06}|Agriculture.API.Write\\Agriculture.API.Write.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.api.write\\application\\handler\\customeraddhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7B851BAA-238C-4D62-B6A4-7928BDD32A06}|Agriculture.API.Write\\Agriculture.API.Write.csproj|solutionrelative:agriculture.api.write\\application\\handler\\customeraddhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.api.read\\application\\handler\\customerqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|solutionrelative:agriculture.api.read\\application\\handler\\customerqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.api.read\\application\\handler\\supplierdetailhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|solutionrelative:agriculture.api.read\\application\\handler\\supplierdetailhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.api.read\\application\\handler\\customerdetailhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{988DF834-80E4-473E-99D4-A567589BE144}|Agriculture.API.Read\\Agriculture.API.Read.csproj|solutionrelative:agriculture.api.read\\application\\handler\\customerdetailhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{5EB5D63E-1BBA-44AD-8343-805CCAF56301}|Agriculture.Domain\\Agriculture.Domain.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.domain\\entities\\customeraccount.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{5EB5D63E-1BBA-44AD-8343-805CCAF56301}|Agriculture.Domain\\Agriculture.Domain.csproj|solutionrelative:agriculture.domain\\entities\\customeraccount.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7B851BAA-238C-4D62-B6A4-7928BDD32A06}|Agriculture.API.Write\\Agriculture.API.Write.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.api.write\\controllers\\managementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7B851BAA-238C-4D62-B6A4-7928BDD32A06}|Agriculture.API.Write\\Agriculture.API.Write.csproj|solutionrelative:agriculture.api.write\\controllers\\managementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B64B8DA4-F4DF-42B1-BDFE-9460D1E6E49D}|Agriculture.ErrorCode\\Agriculture.ErrorCode.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.errorcode\\apienums.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B64B8DA4-F4DF-42B1-BDFE-9460D1E6E49D}|Agriculture.ErrorCode\\Agriculture.ErrorCode.csproj|solutionrelative:agriculture.errorcode\\apienums.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B64B8DA4-F4DF-42B1-BDFE-9460D1E6E49D}|Agriculture.ErrorCode\\Agriculture.ErrorCode.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.errorcode\\apipageing.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B64B8DA4-F4DF-42B1-BDFE-9460D1E6E49D}|Agriculture.ErrorCode\\Agriculture.ErrorCode.csproj|solutionrelative:agriculture.errorcode\\apipageing.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B64B8DA4-F4DF-42B1-BDFE-9460D1E6E49D}|Agriculture.ErrorCode\\Agriculture.ErrorCode.csproj|d:\\实训一\\项目\\农业\\agricultureorder\\agriculture.errorcode\\apiresult.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B64B8DA4-F4DF-42B1-BDFE-9460D1E6E49D}|Agriculture.ErrorCode\\Agriculture.ErrorCode.csproj|solutionrelative:agriculture.errorcode\\apiresult.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 6, "Title": "SupplierAddHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Write\\Application\\Handler\\SupplierAddHandler.cs", "RelativeDocumentMoniker": "Agriculture.API.Write\\Application\\Handler\\SupplierAddHandler.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Write\\Application\\Handler\\SupplierAddHandler.cs", "RelativeToolTip": "Agriculture.API.Write\\Application\\Handler\\SupplierAddHandler.cs", "ViewState": "AQIAAEMAAAAAAAAAAAAQwFIAAAANAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T00:26:20.58Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Supplier.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.Domain\\Entities\\Supplier.cs", "RelativeDocumentMoniker": "Agriculture.Domain\\Entities\\Supplier.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.Domain\\Entities\\Supplier.cs", "RelativeToolTip": "Agriculture.Domain\\Entities\\Supplier.cs", "ViewState": "AQIAAF0AAAAAAAAAAAAuwBoAAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T06:10:04.463Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "SupplierProductPrice.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.Domain\\Entities\\SupplierProductPrice.cs", "RelativeDocumentMoniker": "Agriculture.Domain\\Entities\\SupplierProductPrice.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.Domain\\Entities\\SupplierProductPrice.cs", "RelativeToolTip": "Agriculture.Domain\\Entities\\SupplierProductPrice.cs", "ViewState": "AQIAACMAAAAAAAAAAADwvzkAAAAjAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T07:29:54.233Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "SupplierQueryHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\Handler\\SupplierQueryHandler.cs", "RelativeDocumentMoniker": "Agriculture.API.Read\\Application\\Handler\\SupplierQueryHandler.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\Handler\\SupplierQueryHandler.cs", "RelativeToolTip": "Agriculture.API.Read\\Application\\Handler\\SupplierQueryHandler.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAmwBYAAAANAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T06:32:35.041Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "SupplierQuerycommand.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\command\\SupplierQuerycommand.cs", "RelativeDocumentMoniker": "Agriculture.API.Read\\Application\\command\\SupplierQuerycommand.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\command\\SupplierQuerycommand.cs", "RelativeToolTip": "Agriculture.API.Read\\Application\\command\\SupplierQuerycommand.cs", "ViewState": "AQIAABcAAAAAAAAAAAAwwAoAAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T06:32:00.114Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MyDbcontext.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.Infrastuctrue\\MyDbcontext.cs", "RelativeDocumentMoniker": "Agriculture.Infrastuctrue\\MyDbcontext.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.Infrastuctrue\\MyDbcontext.cs", "RelativeToolTip": "Agriculture.Infrastuctrue\\MyDbcontext.cs", "ViewState": "AQIAAAUAAAAAAAAAAAAzwAkAAAAJAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T06:48:40.188Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ManagementController.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Controllers\\ManagementController.cs", "RelativeDocumentMoniker": "Agriculture.API.Read\\Controllers\\ManagementController.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Controllers\\ManagementController.cs", "RelativeToolTip": "Agriculture.API.Read\\Controllers\\ManagementController.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAwAAAAkAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T06:41:06.614Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "CustomerAddHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Write\\Application\\Handler\\CustomerAddHandler.cs", "RelativeDocumentMoniker": "Agriculture.API.Write\\Application\\Handler\\CustomerAddHandler.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Write\\Application\\Handler\\CustomerAddHandler.cs", "RelativeToolTip": "Agriculture.API.Write\\Application\\Handler\\CustomerAddHandler.cs", "ViewState": "AQIAAEsAAAAAAAAAAAAowGMAAAANAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T00:25:55.937Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "CustomerQueryHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\Handler\\CustomerQueryHandler.cs", "RelativeDocumentMoniker": "Agriculture.API.Read\\Application\\Handler\\CustomerQueryHandler.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\Handler\\CustomerQueryHandler.cs", "RelativeToolTip": "Agriculture.API.Read\\Application\\Handler\\CustomerQueryHandler.cs", "ViewState": "AQIAAB8AAAAAAAAAAAAQwFYAAAAaAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T00:25:38.733Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "SupplierDetailHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\Handler\\SupplierDetailHandler.cs", "RelativeDocumentMoniker": "Agriculture.API.Read\\Application\\Handler\\SupplierDetailHandler.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\Handler\\SupplierDetailHandler.cs", "RelativeToolTip": "Agriculture.API.Read\\Application\\Handler\\SupplierDetailHandler.cs", "ViewState": "AQIAABQAAAAAAAAAAAAgwGQAAAAnAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T00:25:30.186Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "CustomerDetailHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\Handler\\CustomerDetailHandler.cs", "RelativeDocumentMoniker": "Agriculture.API.Read\\Application\\Handler\\CustomerDetailHandler.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Read\\Application\\Handler\\CustomerDetailHandler.cs", "RelativeToolTip": "Agriculture.API.Read\\Application\\Handler\\CustomerDetailHandler.cs", "ViewState": "AQIAABIAAAAAAAAAAAAgwCYAAAAnAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T00:25:11.166Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "CustomerAccount.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.Domain\\Entities\\CustomerAccount.cs", "RelativeDocumentMoniker": "Agriculture.Domain\\Entities\\CustomerAccount.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.Domain\\Entities\\CustomerAccount.cs", "RelativeToolTip": "Agriculture.Domain\\Entities\\CustomerAccount.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAACsAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T07:19:58.01Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ManagementController.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Write\\Controllers\\ManagementController.cs", "RelativeDocumentMoniker": "Agriculture.API.Write\\Controllers\\ManagementController.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.API.Write\\Controllers\\ManagementController.cs", "RelativeToolTip": "Agriculture.API.Write\\Controllers\\ManagementController.cs", "ViewState": "AQIAABAAAAAAAAAAAAAgwAQAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T05:47:30.745Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "APIPageing.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.ErrorCode\\APIPageing.cs", "RelativeDocumentMoniker": "Agriculture.ErrorCode\\APIPageing.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.ErrorCode\\APIPageing.cs", "RelativeToolTip": "Agriculture.ErrorCode\\APIPageing.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAgAAAARAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T07:01:25.995Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "APIEnums.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.ErrorCode\\APIEnums.cs", "RelativeDocumentMoniker": "Agriculture.ErrorCode\\APIEnums.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.ErrorCode\\APIEnums.cs", "RelativeToolTip": "Agriculture.ErrorCode\\APIEnums.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAwAAAAaAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T07:00:49.516Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "APIResult.cs", "DocumentMoniker": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.ErrorCode\\APIResult.cs", "RelativeDocumentMoniker": "Agriculture.ErrorCode\\APIResult.cs", "ToolTip": "D:\\实训一\\项目\\农业\\AgricultureOrder\\Agriculture.ErrorCode\\APIResult.cs", "RelativeToolTip": "Agriculture.ErrorCode\\APIResult.cs", "ViewState": "AQIAAAAAAAAAAAAAAIBMwBQAAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T07:01:36.044Z"}]}]}]}
<template>
  <div class="quick-nav">
    <el-card class="nav-card" shadow="never">
      <template #header>
        <div class="nav-header">
          <span>快速导航</span>
          <el-tag type="info" size="small">{{ currentModule }}</el-tag>
        </div>
      </template>
      
      <div class="nav-buttons">
        <el-button
          type="primary"
          :plain="currentPath !== '/supplier'"
          @click="navigateTo('/supplier')"
          class="nav-btn"
        >
          <el-icon><User /></el-icon>
          供应商管理
        </el-button>
        
        <el-button
          type="success"
          :plain="currentPath !== '/customer'"
          @click="navigateTo('/customer')"
          class="nav-btn"
        >
          <el-icon><UserFilled /></el-icon>
          客户管理
        </el-button>
      </div>
      

    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { User, UserFilled } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 当前路径
const currentPath = computed(() => route.path)

// 当前模块名称
const currentModule = computed(() => {
  const moduleMap: Record<string, string> = {
    '/supplier': '供应商管理',
    '/customer': '客户管理'
  }
  return moduleMap[currentPath.value] || '业务管理'
})

// 导航方法
const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.quick-nav {
  margin-bottom: 20px;
}

.nav-card {
  border: 1px solid #e4e7ed;
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.nav-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.nav-btn {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-buttons {
    flex-direction: column;
  }
}
</style> 
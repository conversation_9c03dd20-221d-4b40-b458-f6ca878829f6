# 数据库迁移脚本
# 使用方法: .\migrate-database.ps1

Write-Host "开始数据库迁移..." -ForegroundColor Green

# 设置工作目录为解决方案根目录
Set-Location $PSScriptRoot

# 检查是否存在迁移
Write-Host "检查现有迁移..." -ForegroundColor Yellow
dotnet ef migrations list --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write

# 创建初始迁移
Write-Host "创建初始迁移..." -ForegroundColor Yellow
dotnet ef migrations add InitialCreate --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write

# 应用迁移到数据库
Write-Host "应用迁移到数据库..." -ForegroundColor Yellow
dotnet ef database update --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write

Write-Host "数据库迁移完成!" -ForegroundColor Green 
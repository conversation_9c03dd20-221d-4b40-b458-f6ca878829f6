using Agriculture.API.Read;
using IGeekFan.AspNetCore.Knife4jUI;

var builder = WebApplication.CreateBuilder(args).Inject();

// Add services to the container.



var app = builder.Build();

// Configure the HTTP request pipeline.

    app.UseSwagger();
    app.UseKnife4UI();

app.UseCors(x => x.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod());

app.UseAuthorization();

app.MapControllers();

app.Run();

using Agriculture.API.Write.Application.command;
using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using Agriculture.Infrastuctrue;
using MediatR;

namespace Agriculture.API.Write.Application.Handler
{
    /// <summary>
    /// 客户添加处理器
    /// </summary>
    public class CustomerAddHandler : IRequestHandler<CustomerAddCommand, APIResult<Customer>>
    {
        private readonly IBaseRepository<Customer> _customerRepository;

        public CustomerAddHandler(IBaseRepository<Customer> customerRepository)
        {
            _customerRepository = customerRepository;
        }

        public async Task<APIResult<Customer>> Handle(CustomerAddCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<Customer>();

            try
            {
                // 验证必填字段
                if (string.IsNullOrWhiteSpace(request.CustomerName))
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "客户名称不能为空";
                    return result;
                }

                if (string.IsNullOrWhiteSpace(request.CustomerNumber))
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "客户编号不能为空";
                    return result;
                }

                // 检查客户编号是否已存在
                var existingCustomer = _customerRepository.GetAll()
                    .FirstOrDefault(c => c.CustomerNumber == request.CustomerNumber);
                
                if (existingCustomer != null)
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "客户编号已存在，请使用其他编号";
                    return result;
                }

                // 验证上级客户是否存在
                if (request.SuperiorCustomerId.HasValue)
                {
                    var superiorCustomer = await _customerRepository.GetModel(request.SuperiorCustomerId.Value);
                    if (superiorCustomer == null)
                    {
                        result.Code = ResultCode.Fail;
                        result.Message = "上级客户不存在";
                        return result;
                    }
                }

                // 创建新的客户实体
                var customer = new Customer
                {
                    CustomerNumber = request.CustomerNumber,
                    CustomerName = request.CustomerName,
                    CustomerType = request.CustomerType,
                    PersonInChargeName = request.PersonInChargeName,
                    PersonInChargePhone = request.PersonInChargePhone,
                    IdCard = request.IdCard,
                    Location = request.Location,
                    Industry = request.Industry,
                    UnifiedSocialCreditCode = request.UnifiedSocialCreditCode,
                    MnemonicCode = request.MnemonicCode,
                    CustomerStage = request.CustomerStage,
                    CustomTags = request.CustomTags,
                    SuperiorCustomerId = request.SuperiorCustomerId,
                    UsageStatus = request.UsageStatus,
                    CustomerLevel = request.CustomerLevel,
                    CustomerGroupClassification = request.CustomerGroupClassification,
                    EntryUnit = request.EntryUnit,
                    UsageUnit = request.UsageUnit,
                    Remarks = request.Remarks,
                    IsArchived = request.IsArchived,
                    Longitude = request.Longitude,
                    Latitude = request.Latitude,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                // 保存到数据库
                await _customerRepository.AddAsync(customer);

                result.Code = ResultCode.Success;
                result.Message = "客户添加成功";
                result.Data = customer;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"添加客户失败：{ex.Message}";
            }

            return result;
        }
    }
} 
### 客户管理API测试

@baseUrl = http://localhost:5101

### 添加企业客户
POST {{baseUrl}}/api/Management/AddCustomer
Content-Type: application/json

{
  "customerNumber": "CUS001",
  "customerName": "测试企业客户",
  "customerType": "企业",
  "personInChargeName": "王五",
  "personInChargePhone": "13700137000",
  "idCard": "110101199003033456",
  "location": "广州市天河区",
  "industry": "农业",
  "unifiedSocialCreditCode": "91440101MA00123456",
  "mnemonicCode": "CSKH",
  "customerStage": "潜在客户",
  "customTags": "重要客户,农业企业",
  "usageStatus": "启用",
  "customerLevel": "A级",
  "customerGroupClassification": "农业集团",
  "entryUnit": "系统管理员",
  "usageUnit": "销售部",
  "remarks": "测试企业客户备注",
  "isArchived": false,
  "longitude": 113.2644,
  "latitude": 23.1291
}

### 添加个人客户
POST {{baseUrl}}/api/Management/AddCustomer
Content-Type: application/json

{
  "customerNumber": "CUS002",
  "customerName": "个人客户",
  "customerType": "个人",
  "personInChargeName": "赵六",
  "personInChargePhone": "13600136000",
  "idCard": "110101199004044567",
  "location": "深圳市南山区",
  "industry": "农产品",
  "mnemonicCode": "GRKH",
  "customerStage": "意向客户",
  "customTags": "个人客户",
  "usageStatus": "启用",
  "customerLevel": "B级",
  "entryUnit": "系统管理员",
  "usageUnit": "销售部",
  "remarks": "个人客户备注"
}

### 添加有上级客户的客户
POST {{baseUrl}}/api/Management/AddCustomer
Content-Type: application/json

{
  "customerNumber": "CUS003",
  "customerName": "子公司客户",
  "customerType": "企业",
  "personInChargeName": "孙七",
  "personInChargePhone": "13500135000",
  "location": "上海市浦东新区",
  "industry": "农业科技",
  "unifiedSocialCreditCode": "91310115MA00123456",
  "mnemonicCode": "ZGSKH",
  "customerStage": "成交客户",
  "customTags": "子公司,重要客户",
  "superiorCustomerId": 1,
  "usageStatus": "启用",
  "customerLevel": "A级",
  "customerGroupClassification": "农业集团",
  "entryUnit": "系统管理员",
  "usageUnit": "销售部",
  "remarks": "子公司客户备注"
}

### 添加停用状态的客户
POST {{baseUrl}}/api/Management/AddCustomer
Content-Type: application/json

{
  "customerNumber": "CUS004",
  "customerName": "停用客户",
  "customerType": "企业",
  "personInChargeName": "周八",
  "personInChargePhone": "13400134000",
  "location": "北京市朝阳区",
  "industry": "农业",
  "mnemonicCode": "TYKH",
  "customerStage": "流失客户",
  "customTags": "停用客户",
  "usageStatus": "停用",
  "customerLevel": "C级",
  "entryUnit": "系统管理员",
  "usageUnit": "销售部",
  "remarks": "停用客户备注"
} 
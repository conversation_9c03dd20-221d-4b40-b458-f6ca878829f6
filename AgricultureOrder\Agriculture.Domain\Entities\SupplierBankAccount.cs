using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 供应商银行账户
    /// </summary>
    public class SupplierBankAccount : BaseEntity
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public long SupplierId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public virtual Supplier Supplier { get; set; } = null!;

        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankName { get; set; } = string.Empty;

        /// <summary>
        /// 开户行
        /// </summary>
        public string? BranchName { get; set; }

        /// <summary>
        /// 账户名称
        /// </summary>
        public string AccountName { get; set; } = string.Empty;

        /// <summary>
        /// 银行账号
        /// </summary>
        public string AccountNumber { get; set; } = string.Empty;

        /// <summary>
        /// 是否默认账户
        /// </summary>
        public bool IsDefault { get; set; } = false;
    }
} 
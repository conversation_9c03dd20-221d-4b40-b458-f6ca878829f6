import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/home',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/map-demo',
      name: 'map-demo',
      component: () => import('../views/MapDemo.vue'),
      meta: {
        title: '地图功能演示'
      }
    },
    {
      path: '/test-map',
      name: 'test-map',
      component: () => import('../views/TestMap.vue'),
      meta: {
        title: '地图功能测试'
      }
    },
    {
      path: '/search-test',
      name: 'search-test',
      component: () => import('../views/SearchTest.vue'),
      meta: {
        title: '搜索功能测试'
      }
    },
    {
      path: '/baidu-map-test',
      name: 'baidu-map-test',
      component: () => import('../views/BaiduMapTest.vue'),
      meta: {
        title: '百度地图测试'
      }
    },
    {
      path: '/map-test',
      name: 'map-test',
      component: () => import('../views/MapTest.vue'),
      meta: {
        title: '地图功能测试'
      }
    },
    {
      path: '/',
      name: 'management',
      component: () => import('../views/Agriculture/ManagementLayout.vue'),
      children: [
        {
          path: 'supplier',
          name: 'supplier',
          component: () => import('../views/Agriculture/supplierShow.vue'),
          meta: {
            title: '供应商管理'
          }
        },
        {
          path: 'customer',
          name: 'customer',
          component: () => import('../views/Agriculture/CustomerShow.vue'),
          meta: {
            title: '客户管理'
          }
        }
      ]
    },
  ],
})

export default router

# 供应商关联数据插入脚本
# 请确保MySQL服务正在运行，并且连接信息正确

$connectionString = "Server=*************;uid=root;pwd=***********;database=AgricultureOrder"

Write-Host "正在插入供应商关联数据..." -ForegroundColor Green

# 读取SQL文件内容
$sqlContent = Get-Content -Path "insert-supplier-related-data.sql" -Raw -Encoding UTF8

# 使用MySQL命令行工具执行SQL
try {
    # 分割SQL语句（按分号分割）
    $sqlStatements = $sqlContent -split ';' | Where-Object { $_.Trim() -ne '' }
    
    foreach ($statement in $sqlStatements) {
        $cleanStatement = $statement.Trim()
        if ($cleanStatement -ne '') {
            Write-Host "执行SQL语句: $($cleanStatement.Substring(0, [Math]::Min(50, $cleanStatement.Length)))..." -ForegroundColor Yellow
            
            # 使用mysql命令行工具执行
            $tempFile = [System.IO.Path]::GetTempFileName()
            $cleanStatement | Out-File -FilePath $tempFile -Encoding UTF8
            
            mysql -h ************* -u root -p*********** AgricultureOrder -e "source $tempFile"
            
            # 删除临时文件
            Remove-Item $tempFile
            
            Write-Host "SQL语句执行成功" -ForegroundColor Green
        }
    }
    
    Write-Host "所有供应商关联数据插入完成！" -ForegroundColor Green
}
catch {
    Write-Host "执行SQL时发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查MySQL连接信息和数据库状态" -ForegroundColor Red
}

Write-Host "脚本执行完成" -ForegroundColor Green 
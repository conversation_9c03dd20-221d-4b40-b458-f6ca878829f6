// 地图配置
export const MAP_CONFIG = {
  // 高德地图API密钥
  // 注意：实际使用时需要替换为真实的API密钥
  // 可以在高德开放平台申请：https://lbs.amap.com/
  AMAP_KEY: import.meta.env.VITE_AMAP_KEY || 'your_amap_key_here',
  
  // 地图默认配置
  DEFAULT_CENTER: [116.397428, 39.90923], // 北京
  DEFAULT_ZOOM: 13,
  
  // 地图样式
  MAP_STYLE: 'amap://styles/normal'
}

// 获取高德地图API密钥
export const getAmapKey = (): string => {
  return MAP_CONFIG.AMAP_KEY
}

// 检查API密钥是否有效
export const isAmapKeyValid = (): boolean => {
  return MAP_CONFIG.AMAP_KEY !== 'your_amap_key_here' && MAP_CONFIG.AMAP_KEY.length > 0
} 
using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 供应商联系信息
    /// </summary>
    public class SupplierContact : BaseEntity
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public long SupplierId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public virtual Supplier Supplier { get; set; } = null!;

        /// <summary>
        /// 联系人姓名
        /// </summary>
        public string ContactName { get; set; } = string.Empty;

        /// <summary>
        /// 联系电话
        /// </summary>
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 联系邮箱
        /// </summary>
        public string? ContactEmail { get; set; }

        /// <summary>
        /// 联系地址
        /// </summary>
        public string? ContactAddress { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        public string? Position { get; set; }

        /// <summary>
        /// 是否主要联系人
        /// </summary>
        public bool IsPrimary { get; set; } = false;
    }
} 
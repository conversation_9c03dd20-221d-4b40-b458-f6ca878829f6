# 地图定位功能使用指南

## 功能概述

现在您可以在新增客户或供应商时，点击"地图定位"按钮，系统会弹出一个包含真实地图界面的对话框，让您能够：

1. **可视化定位**：在真实地图上查看和选择位置
2. **地址搜索**：输入地址进行精确搜索
3. **点击选择**：直接点击地图上的任意位置
4. **坐标获取**：自动获取选中位置的经纬度坐标

## 使用方法

### 1. 在客户新增表单中

1. 填写省份和详细地址（可选）
2. 点击"地图定位"按钮
3. 在弹出的地图对话框中：
   - 可以输入地址进行搜索
   - 可以直接点击地图选择位置
   - 可以点击"我的位置"获取当前位置
4. 选择位置后点击"确认位置"
5. 系统会自动填充经纬度坐标到表单中

### 2. 在供应商新增表单中

使用方法与客户表单完全相同。

### 3. 测试页面

访问 `/test-map` 路由可以测试所有地图功能。

## 地图界面功能

### 地址搜索
- 在搜索框中输入地址关键词
- 点击搜索按钮或按回车键
- 系统会自动定位到搜索结果

### 地图操作
- **点击选择**：点击地图任意位置选择该位置
- **缩放控制**：使用鼠标滚轮或地图控件进行缩放
- **拖拽移动**：拖拽地图查看不同区域

### 位置信息
- 实时显示选中位置的详细地址
- 显示精确的经纬度坐标
- 显示定位状态

### 控制按钮
- **确认位置**：确认当前选择的位置
- **清除选择**：清除已选择的位置
- **我的位置**：获取浏览器当前位置

## 技术特性

### 智能模式切换
- **有API密钥**：使用真实的百度地图API，提供精确的地址搜索和坐标获取
- **无API密钥**：自动切换到模拟模式，提供基本的地图功能演示

### 响应式设计
- 适配不同屏幕尺寸
- 移动端友好的界面
- 流畅的用户交互体验

### 错误处理
- 网络错误自动重试
- API调用失败友好提示
- 加载状态实时反馈

## 配置说明

### 1. 配置百度地图API（推荐）

1. 访问 [百度地图开放平台](https://lbsyun.baidu.com/)
2. 注册账号并创建应用
3. 获取API密钥
4. 在项目根目录创建 `.env.local` 文件：
   ```bash
   VITE_BAIDU_API_KEY=your_actual_api_key_here
   ```
5. 重启开发服务器

### 2. 使用模拟模式（无需配置）

如果不配置API密钥，系统会自动使用模拟模式，提供基本的地图功能演示。

## 文件结构

```
src/
├── components/
│   ├── BaiduMap.vue             # 百度地图组件
│   └── MapLocationDialog.vue    # 地图定位对话框
├── services/
│   └── mapService.ts            # 地图服务
├── views/
│   ├── Agriculture/components/
│   │   ├── CustomerForm.vue     # 客户表单（已集成地图定位）
│   │   └── SupplierForm.vue     # 供应商表单（已集成地图定位）
│   ├── MapDemo.vue              # 地图功能演示
│   └── TestMap.vue              # 地图功能测试
└── env.example                  # 环境变量示例
```

## 常见问题

### Q: 地图无法加载怎么办？
A: 
1. 检查网络连接
2. 确认API密钥是否正确配置
3. 查看浏览器控制台错误信息
4. 尝试刷新页面

### Q: 地址搜索不准确怎么办？
A:
1. 使用更详细的地址信息
2. 确保地址包含城市和区域信息
3. 尝试不同的地址表述方式

### Q: 如何获取当前位置？
A:
1. 点击"我的位置"按钮
2. 允许浏览器获取位置权限
3. 系统会自动定位到当前位置

### Q: 坐标数据会保存到哪里？
A:
- 经纬度坐标会保存到数据库的 `Longitude` 和 `Latitude` 字段
- 地址信息会保存到 `Location` 字段
- 提交表单时会自动验证和获取缺失的坐标

## 注意事项

1. **API密钥安全**：请妥善保管API密钥，不要提交到版本控制系统
2. **使用限制**：百度地图API有使用频率限制，请合理使用
3. **网络要求**：需要网络连接才能使用地图服务
4. **浏览器兼容性**：建议使用现代浏览器以获得最佳体验
5. **隐私保护**：获取当前位置需要用户授权

## 技术支持

如有问题，请参考：
- 百度地图API文档：https://lbsyun.baidu.com/index.php?title=webapi
- 项目GitHub仓库
- 测试页面：`/test-map`
- 演示页面：`/map-demo` 
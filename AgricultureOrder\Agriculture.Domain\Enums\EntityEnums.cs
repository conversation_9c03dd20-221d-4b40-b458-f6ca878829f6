namespace Agriculture.Domain.Enums
{
    /// <summary>
    /// 实体类型枚举
    /// </summary>
    public enum EntityType
    {
        /// <summary>
        /// 个人
        /// </summary>
        Individual = 1,

        /// <summary>
        /// 企业
        /// </summary>
        Enterprise = 2
    }

    /// <summary>
    /// 使用状态枚举
    /// </summary>
    public enum UsageStatus
    {
        /// <summary>
        /// 启用
        /// </summary>
        Enabled = 1,

        /// <summary>
        /// 停用
        /// </summary>
        Disabled = 2
    }

    /// <summary>
    /// 客户级别枚举
    /// </summary>
    public enum CustomerLevel
    {
        /// <summary>
        /// 一级
        /// </summary>
        LevelOne = 1,

        /// <summary>
        /// 二级
        /// </summary>
        LevelTwo = 2,

        /// <summary>
        /// 三级
        /// </summary>
        LevelThree = 3
    }

    /// <summary>
    /// 服务状态枚举
    /// </summary>
    public enum ServiceStatus
    {
        /// <summary>
        /// 服务中
        /// </summary>
        Active = 1,

        /// <summary>
        /// 已结束
        /// </summary>
        Ended = 2,

        /// <summary>
        /// 暂停
        /// </summary>
        Paused = 3
    }

    /// <summary>
    /// 关联状态枚举
    /// </summary>
    public enum AssociationStatus
    {
        /// <summary>
        /// 已关联
        /// </summary>
        Associated = 1,

        /// <summary>
        /// 未关联
        /// </summary>
        NotAssociated = 2,

        /// <summary>
        /// 已解除
        /// </summary>
        Disassociated = 3
    }
} 
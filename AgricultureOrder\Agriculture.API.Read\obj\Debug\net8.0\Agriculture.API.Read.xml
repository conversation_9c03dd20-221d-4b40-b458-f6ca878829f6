<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Agriculture.API.Read</name>
    </assembly>
    <members>
        <member name="T:Agriculture.API.Read.Application.command.CustomerDetailCommand">
            <summary>
            客户详情查询命令
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.CustomerDetailCommand.Id">
            <summary>
            客户ID
            </summary>
        </member>
        <member name="T:Agriculture.API.Read.Application.command.CustomerQueryCommand">
            <summary>
            客户查询命令
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.CustomerQueryCommand.CustomerName">
            <summary>
            客户名称（支持模糊查询）
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.CustomerQueryCommand.CustomerNumber">
            <summary>
            客户编号
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.CustomerQueryCommand.CustomerType">
            <summary>
            客户类型
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.CustomerQueryCommand.CustomerLevel">
            <summary>
            客户级别
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.CustomerQueryCommand.UsageStatus">
            <summary>
            使用状态
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.CustomerQueryCommand.PageIndex">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.CustomerQueryCommand.PageSize">
            <summary>
            每页大小
            </summary>
        </member>
        <member name="T:Agriculture.API.Read.Application.command.SupplierDetailCommand">
            <summary>
            供应商详情查询命令
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.SupplierDetailCommand.Id">
            <summary>
            供应商ID
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.SupplierQuerycommand.SupplierNumber">
            <summary>
            供应商编号
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.SupplierQuerycommand.SupplierName">
            <summary>
            供应商名称
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.SupplierQuerycommand.SupplierType">
            <summary>
            供应商类型
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.SupplierQuerycommand.UsageStatus">
            <summary>
            使用状态
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.SupplierQuerycommand.PageIndex">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:Agriculture.API.Read.Application.command.SupplierQuerycommand.PageSize">
            <summary>
            每页大小
            </summary>
        </member>
        <member name="T:Agriculture.API.Read.Application.Handler.CustomerDetailHandler">
            <summary>
            客户详情查询处理器
            </summary>
        </member>
        <member name="T:Agriculture.API.Read.Application.Handler.CustomerQueryHandler">
            <summary>
            客户查询处理器
            </summary>
        </member>
        <member name="T:Agriculture.API.Read.Application.Handler.SupplierDetailHandler">
            <summary>
            供应商详情查询处理器
            </summary>
        </member>
        <member name="T:Agriculture.API.Read.Controllers.ManagementController">
            <summary>
            管理控制器
            </summary>
        </member>
        <member name="M:Agriculture.API.Read.Controllers.ManagementController.GetSupplier(Agriculture.API.Read.Application.command.SupplierQuerycommand)">
            <summary>
            获取供应商列表
            </summary>
            <param name="request">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Agriculture.API.Read.Controllers.ManagementController.GetSupplierDetail(System.Int64)">
            <summary>
            获取供应商详情（反填功能）
            </summary>
            <param name="id">供应商ID</param>
            <returns>供应商详情</returns>
        </member>
        <member name="M:Agriculture.API.Read.Controllers.ManagementController.GetCustomer(Agriculture.API.Read.Application.command.CustomerQueryCommand)">
            <summary>
            获取客户列表
            </summary>
            <param name="request">查询命令</param>
            <returns>客户列表</returns>
        </member>
        <member name="M:Agriculture.API.Read.Controllers.ManagementController.GetCustomerDetail(System.Int64)">
            <summary>
            获取客户详情（反填功能）
            </summary>
            <param name="id">客户ID</param>
            <returns>客户详情</returns>
        </member>
        <member name="T:Agriculture.WeatherForecast">
            <summary>
            天气预报模型
            </summary>
        </member>
        <member name="P:Agriculture.WeatherForecast.Date">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:Agriculture.WeatherForecast.TemperatureC">
            <summary>
            摄氏温度
            </summary>
        </member>
        <member name="P:Agriculture.WeatherForecast.TemperatureF">
            <summary>
            华氏温度
            </summary>
        </member>
        <member name="P:Agriculture.WeatherForecast.Summary">
            <summary>
            天气摘要
            </summary>
        </member>
    </members>
</doc>

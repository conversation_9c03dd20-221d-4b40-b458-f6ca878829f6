using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 供应商附件
    /// </summary>
    public class SupplierAttachment : BaseEntity
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public long SupplierId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public virtual Supplier Supplier { get; set; } = null!;

        /// <summary>
        /// 附件名称
        /// </summary>
        public string AttachmentName { get; set; } = string.Empty;

        /// <summary>
        /// 附件类型
        /// </summary>
        public string? AttachmentType { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string? FileExtension { get; set; }
    }
} 
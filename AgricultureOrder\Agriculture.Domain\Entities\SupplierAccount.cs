using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 供应商关联账户
    /// </summary>
    public class SupplierAccount : BaseEntity
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public long SupplierId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public virtual Supplier Supplier { get; set; } = null!;

        /// <summary>
        /// 账户类型（个人/企业）
        /// </summary>
        public string AccountType { get; set; } = string.Empty;

        /// <summary>
        /// 账户ID号
        /// </summary>
        public string AccountId { get; set; } = string.Empty;

        /// <summary>
        /// 公司全称
        /// </summary>
        public string? CompanyFullName { get; set; }

        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string? PersonInChargeName { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string? MobileNumber { get; set; }
    }
} 
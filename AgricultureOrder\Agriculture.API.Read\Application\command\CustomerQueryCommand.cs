using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using MediatR;

namespace Agriculture.API.Read.Application.command
{
    /// <summary>
    /// 客户查询命令
    /// </summary>
    public class CustomerQueryCommand : IRequest<APIResult<APIPageing<Customer>>>
    {
        /// <summary>
        /// 客户名称（支持模糊查询）
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        public string? CustomerNumber { get; set; }

        /// <summary>
        /// 客户类型
        /// </summary>
        public string? CustomerType { get; set; }

        /// <summary>
        /// 客户级别
        /// </summary>
        public string? CustomerLevel { get; set; }

        /// <summary>
        /// 使用状态
        /// </summary>
        public string? UsageStatus { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
    }
} 
<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<template>
  <main class="home-view">
    <div class="container">
      <div class="header">
        <h1>农业订单管理系统</h1>
        <p>欢迎使用农业订单管理系统，请选择要管理的功能模块</p>
      </div>

      <div class="feature-grid">
        <div class="feature-card" @click="navigateTo('/supplier')">
          <div class="feature-icon">
            <el-icon size="48" color="#409EFF">
              <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path fill="currentColor" d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"/>
                <path fill="currentColor" d="M512 336c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"/>
              </svg>
            </el-icon>
          </div>
          <div class="feature-content">
            <h3>业务管理</h3>
            <p>统一管理供应商和客户信息，提供完整的业务数据管理</p>
            <ul>
              <li>供应商管理</li>
              <li>客户管理</li>
              <li>统一界面操作</li>
              <li>数据统计分析</li>
            </ul>
          </div>
        </div>

        <div class="feature-card disabled">
          <div class="feature-icon">
            <el-icon size="48" color="#E6A23C">
              <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path fill="currentColor" d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"/>
                <path fill="currentColor" d="M512 336c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"/>
              </svg>
            </el-icon>
          </div>
          <div class="feature-content">
            <h3>订单管理</h3>
            <p>管理农业订单，包括订单创建、跟踪、结算</p>
            <ul>
              <li>订单创建与编辑</li>
              <li>订单状态跟踪</li>
              <li>订单结算管理</li>
              <li>订单统计分析</li>
            </ul>
            <div class="coming-soon">即将推出</div>
          </div>
        </div>

        <div class="feature-card disabled">
          <div class="feature-icon">
            <el-icon size="48" color="#F56C6C">
              <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path fill="currentColor" d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"/>
                <path fill="currentColor" d="M512 336c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"/>
              </svg>
            </el-icon>
          </div>
          <div class="feature-content">
            <h3>产品管理</h3>
            <p>管理农业产品信息，包括产品分类、价格、库存</p>
            <ul>
              <li>产品信息管理</li>
              <li>产品分类管理</li>
              <li>价格策略设置</li>
              <li>库存管理</li>
            </ul>
            <div class="coming-soon">即将推出</div>
          </div>
        </div>
      </div>

      <div class="system-info">
        <h3>系统信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>系统版本：</label>
            <span>v1.0.0</span>
          </div>
          <div class="info-item">
            <label>技术栈：</label>
            <span>Vue 3 + TypeScript + Element Plus</span>
          </div>
          <div class="info-item">
            <label>后端API：</label>
            <span>.NET 8 + Entity Framework</span>
          </div>
          <div class="info-item">
            <label>数据库：</label>
            <span>SQL Server</span>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<style scoped>
.home-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 300;
}

.header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.feature-card:hover:not(.disabled) {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.feature-icon {
  text-align: center;
  margin-bottom: 20px;
}

.feature-content h3 {
  font-size: 1.5rem;
  color: #303133;
  margin-bottom: 10px;
  text-align: center;
}

.feature-content p {
  color: #606266;
  margin-bottom: 15px;
  text-align: center;
  line-height: 1.6;
}

.feature-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-content li {
  color: #606266;
  padding: 5px 0;
  position: relative;
  padding-left: 20px;
}

.feature-content li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #67C23A;
  font-weight: bold;
}

.coming-soon {
  position: absolute;
  top: 20px;
  right: 20px;
  background: #E6A23C;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.system-info {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.system-info h3 {
  color: #303133;
  margin-bottom: 20px;
  text-align: center;
  font-size: 1.3rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

@media (max-width: 768px) {
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .feature-card {
    padding: 20px;
  }
}
</style>

<template>
  <div class="supplier-detail">
    <div v-loading="loading">
      <div v-if="supplierData" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>供应商编号：</label>
                <span>{{ supplierData.supplierNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>供应商名称：</label>
                <span>{{ supplierData.supplierName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>供应商类型：</label>
                <span>{{ supplierData.supplierType }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>负责人姓名：</label>
                <span>{{ supplierData.personInChargeName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>负责人手机号：</label>
                <span>{{ supplierData.personInChargePhone || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>身份证号：</label>
                <span>{{ supplierData.idCard || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>所在地：</label>
                <span>{{ supplierData.location || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>所属行业：</label>
                <span>{{ supplierData.industry || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>使用状态：</label>
                <el-tag :type="supplierData.usageStatus ? 'success' : 'danger'">
                  {{ supplierData.usageStatus ? '启用' : '停用' }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 企业信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>企业信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="detail-item">
                <label>统一社会信用代码：</label>
                <span>{{ supplierData.unifiedSocialCreditCode || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>助记码：</label>
                <span>{{ supplierData.mnemonicCode || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>录入单位：</label>
                <span>{{ supplierData.entryUnit || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>使用单位：</label>
                <span>{{ supplierData.usageUnit || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 位置信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>位置信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>地图定位经度：</label>
                <span>{{ supplierData.longitude || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>地图定位纬度：</label>
                <span>{{ supplierData.latitude || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 其他信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>其他信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>归集档案：</label>
                <el-tag :type="supplierData.isArchived ? 'warning' : 'info'">
                  {{ supplierData.isArchived ? '是' : '否' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>创建时间：</label>
                <span>{{ formatDate(supplierData.createTime) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="detail-item">
                <label>备注：</label>
                <span>{{ supplierData.remarks || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 关联信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>关联信息</span>
            </div>
          </template>
          
          <!-- 银行账户 -->
          <div class="section-title">银行账户</div>
          <el-table :data="supplierData.bankAccounts || []" stripe empty-text="暂无银行账户信息">
            <el-table-column prop="bankName" label="银行名称" />
            <el-table-column prop="accountNumber" label="账号" />
            <el-table-column prop="accountName" label="账户名称" />
            <el-table-column prop="isDefault" label="是否默认">
              <template #default="{ row }">
                <el-tag :type="row.isDefault ? 'success' : 'info'">
                  {{ row.isDefault ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <!-- 联系信息 -->
          <div class="section-title">联系信息</div>
          <el-table :data="supplierData.contacts || []" stripe empty-text="暂无联系信息">
            <el-table-column prop="contactName" label="联系人" />
            <el-table-column prop="contactPhone" label="联系电话" />
            <el-table-column prop="contactEmail" label="邮箱" />
            <el-table-column prop="position" label="职位" />
            <el-table-column prop="isPrimary" label="主要联系人">
              <template #default="{ row }">
                <el-tag :type="row.isPrimary ? 'success' : 'info'">
                  {{ row.isPrimary ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <!-- 服务人员 -->
          <div class="section-title">服务人员</div>
          
          <el-table :data="supplierData?.servicePersons || []" stripe empty-text="暂无服务人员信息">
            <el-table-column prop="affiliatedMarket" label="所属市场" />
            <el-table-column prop="salespersonName" label="业务员" />
            <el-table-column prop="salespersonId" label="业务员ID" />
          </el-table>
        </el-card>
      </div>

      <div v-else-if="!loading" class="no-data">
        <el-empty description="未找到供应商信息" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { supplierApi, type Supplier } from '@/services/api'

// 定义props
interface Props {
  supplierId: number
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const supplierData = ref<Supplier | null>(null)

// 获取供应商详情
const getSupplierDetail = async () => {
  loading.value = true
  try {
    // 直接使用fetch调用API，绕过API服务层
    const response = await fetch(`http://localhost:5102/api/Management/GetSupplierDetail?id=${props.supplierId}`)
    const rawResult = await response.json()
    
    if (rawResult.code === 200) {
      // 直接设置数据，不使用API服务层
      supplierData.value = {
        ...rawResult.data,
        servicePersons: rawResult.data.servicePersons || []
      }
      
      // 强制重新设置服务人员数据以确保响应式更新
      if (rawResult.data.servicePersons && Array.isArray(rawResult.data.servicePersons)) {
        supplierData.value.servicePersons = [...rawResult.data.servicePersons]
      }
    } else {
      ElMessage.error(result.message || '获取供应商详情失败')
    }
  } catch (error) {
    console.error('获取供应商详情错误:', error)
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleString()
}



// 组件挂载时获取数据
onMounted(() => {
  if (props.supplierId) {
    getSupplierDetail()
  }
})
</script>

<style scoped>
.supplier-detail {
  padding: 20px 0;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-card {
  border: 1px solid #ebeef5;
}

.card-header {
  font-weight: bold;
  color: #303133;
}

.detail-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.detail-item label {
  font-weight: 500;
  color: #606266;
  min-width: 140px;
  margin-right: 8px;
  white-space: nowrap;
}

.detail-item span {
  color: #303133;
  flex: 1;
}

.section-title {
  font-weight: bold;
  color: #303133;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

:deep(.el-table) {
  margin-bottom: 20px;
}

:deep(.el-card__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}
</style> 
﻿// <auto-generated />
using System;
using Agriculture.Infrastuctrue;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Agriculture.Infrastuctrue.Migrations
{
    [DbContext(typeof(MyDbcontext))]
    partial class MyDbcontextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Agriculture.Domain.Entities.Customer", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("CustomTags")
                        .HasColumnType("longtext");

                    b.Property<string>("CustomerGroupClassification")
                        .HasColumnType("longtext");

                    b.Property<string>("CustomerLevel")
                        .HasColumnType("longtext");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("CustomerNumber")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("CustomerStage")
                        .HasColumnType("longtext");

                    b.Property<string>("CustomerType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("EntryUnit")
                        .HasColumnType("longtext");

                    b.Property<string>("IdCard")
                        .HasColumnType("longtext");

                    b.Property<string>("Industry")
                        .HasColumnType("longtext");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("Location")
                        .HasColumnType("longtext");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("MnemonicCode")
                        .HasColumnType("longtext");

                    b.Property<string>("PersonInChargeName")
                        .HasColumnType("longtext");

                    b.Property<string>("PersonInChargePhone")
                        .HasColumnType("longtext");

                    b.Property<string>("Remarks")
                        .HasColumnType("longtext");

                    b.Property<long?>("SuperiorCustomerId")
                        .HasColumnType("bigint");

                    b.Property<string>("UnifiedSocialCreditCode")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UsageStatus")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("UsageUnit")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.HasIndex("SuperiorCustomerId");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerAccount", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("AccountId")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("AccountType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("AssociationStatus")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("AssociationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CompanyFullName")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MobileNumber")
                        .HasColumnType("longtext");

                    b.Property<string>("PersonInChargeName")
                        .HasColumnType("longtext");

                    b.Property<string>("Remarks")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId")
                        .IsUnique();

                    b.ToTable("CustomerAccounts");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerAttachment", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("AttachmentName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("AttachmentType")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerId")
                        .HasColumnType("bigint");

                    b.Property<string>("FileExtension")
                        .HasColumnType("longtext");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remarks")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UploadTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("CustomerAttachments");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerBankAccount", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("AccountType")
                        .HasColumnType("longtext");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("BranchName")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remarks")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("CustomerBankAccounts");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerContact", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("ContactAddress")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ContactPhone")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Position")
                        .HasColumnType("longtext");

                    b.Property<string>("Remarks")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("CustomerContacts");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerDiscountSolution", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("ApplicableConditions")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("DiscountAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal?>("DiscountRate")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("DiscountSolutionCode")
                        .HasColumnType("longtext");

                    b.Property<string>("DiscountSolutionName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("DiscountType")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("EffectiveTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remarks")
                        .HasColumnType("longtext");

                    b.Property<string>("SolutionStatus")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("CustomerDiscountSolutions");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerProductSolution", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("EffectiveTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remarks")
                        .HasColumnType("longtext");

                    b.Property<string>("SolutionCode")
                        .HasColumnType("longtext");

                    b.Property<string>("SolutionDescription")
                        .HasColumnType("longtext");

                    b.Property<string>("SolutionName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("SolutionStatus")
                        .HasColumnType("longtext");

                    b.Property<string>("SolutionType")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("CustomerProductSolutions");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerServicePerson", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("AffiliatedMarket")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remarks")
                        .HasColumnType("longtext");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<string>("SalespersonName")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("ServiceEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ServiceStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ServiceStatus")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("CustomerServicePersons");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.Supplier", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("EntryUnit")
                        .HasColumnType("longtext");

                    b.Property<string>("IdCard")
                        .HasColumnType("longtext");

                    b.Property<string>("Industry")
                        .HasColumnType("longtext");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("Location")
                        .HasColumnType("longtext");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("MnemonicCode")
                        .HasColumnType("longtext");

                    b.Property<string>("PersonInChargeName")
                        .HasColumnType("longtext");

                    b.Property<string>("PersonInChargePhone")
                        .HasColumnType("longtext");

                    b.Property<string>("Remarks")
                        .HasColumnType("longtext");

                    b.Property<string>("SupplierName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("SupplierNumber")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("SupplierType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("UnifiedSocialCreditCode")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("UsageStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("UsageUnit")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.ToTable("Suppliers");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierAccount", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("AccountId")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("AccountType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("CompanyFullName")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MobileNumber")
                        .HasColumnType("longtext");

                    b.Property<string>("PersonInChargeName")
                        .HasColumnType("longtext");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId")
                        .IsUnique();

                    b.ToTable("SupplierAccounts");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierAttachment", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("AttachmentName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("AttachmentType")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("FileExtension")
                        .HasColumnType("longtext");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierAttachments");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierBankAccount", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("BranchName")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierBankAccounts");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierContact", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("ContactAddress")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ContactPhone")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Position")
                        .HasColumnType("longtext");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierContacts");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierProductPrice", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("PriceEffectiveTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("PriceExpirationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ProductCode")
                        .HasColumnType("longtext");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ProductSpecification")
                        .HasColumnType("longtext");

                    b.Property<decimal>("PurchasePrice")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("QuantityUnit")
                        .HasColumnType("longtext");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierProductPrices");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierServicePerson", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("AffiliatedMarket")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<string>("SalespersonName")
                        .HasColumnType("longtext");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("UpdateUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierServicePersons");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.Customer", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Customer", "SuperiorCustomer")
                        .WithMany()
                        .HasForeignKey("SuperiorCustomerId");

                    b.Navigation("SuperiorCustomer");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerAccount", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Customer", "Customer")
                        .WithOne("AssociatedAccount")
                        .HasForeignKey("Agriculture.Domain.Entities.CustomerAccount", "CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerAttachment", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Customer", "Customer")
                        .WithMany("Attachments")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerBankAccount", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Customer", "Customer")
                        .WithMany("BankAccounts")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerContact", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Customer", "Customer")
                        .WithMany("Contacts")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerDiscountSolution", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Customer", "Customer")
                        .WithMany("DiscountSolutions")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerProductSolution", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Customer", "Customer")
                        .WithMany("ProductSolutions")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.CustomerServicePerson", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Customer", "Customer")
                        .WithMany("ServicePersons")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierAccount", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Supplier", "Supplier")
                        .WithOne("AssociatedAccount")
                        .HasForeignKey("Agriculture.Domain.Entities.SupplierAccount", "SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierAttachment", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Supplier", "Supplier")
                        .WithMany("Attachments")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierBankAccount", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Supplier", "Supplier")
                        .WithMany("BankAccounts")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierContact", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Supplier", "Supplier")
                        .WithMany("Contacts")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierProductPrice", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Supplier", "Supplier")
                        .WithMany("ProductPrices")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.SupplierServicePerson", b =>
                {
                    b.HasOne("Agriculture.Domain.Entities.Supplier", "Supplier")
                        .WithMany("ServicePersons")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.Customer", b =>
                {
                    b.Navigation("AssociatedAccount");

                    b.Navigation("Attachments");

                    b.Navigation("BankAccounts");

                    b.Navigation("Contacts");

                    b.Navigation("DiscountSolutions");

                    b.Navigation("ProductSolutions");

                    b.Navigation("ServicePersons");
                });

            modelBuilder.Entity("Agriculture.Domain.Entities.Supplier", b =>
                {
                    b.Navigation("AssociatedAccount");

                    b.Navigation("Attachments");

                    b.Navigation("BankAccounts");

                    b.Navigation("Contacts");

                    b.Navigation("ProductPrices");

                    b.Navigation("ServicePersons");
                });
#pragma warning restore 612, 618
        }
    }
}

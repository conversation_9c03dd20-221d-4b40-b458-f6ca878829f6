using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 客户关联账户
    /// </summary>
    public class CustomerAccount : BaseEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public virtual Customer Customer { get; set; } = null!;

        /// <summary>
        /// 账户类型（个人/企业）
        /// </summary>
        public string AccountType { get; set; } = string.Empty;

        /// <summary>
        /// 账户ID号
        /// </summary>
        public string AccountId { get; set; } = string.Empty;

        /// <summary>
        /// 公司全称
        /// </summary>
        public string? CompanyFullName { get; set; }

        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string? PersonInChargeName { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string? MobileNumber { get; set; }

        /// <summary>
        /// 关联状态
        /// </summary>
        public string? AssociationStatus { get; set; }

        /// <summary>
        /// 关联时间
        /// </summary>
        public DateTime? AssociationTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
} 
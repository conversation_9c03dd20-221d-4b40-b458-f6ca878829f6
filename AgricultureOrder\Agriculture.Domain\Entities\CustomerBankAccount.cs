using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 客户银行账户
    /// </summary>
    public class CustomerBankAccount : BaseEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public virtual Customer Customer { get; set; } = null!;

        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankName { get; set; } = string.Empty;

        /// <summary>
        /// 开户行
        /// </summary>
        public string? BranchName { get; set; }

        /// <summary>
        /// 账户名称
        /// </summary>
        public string AccountName { get; set; } = string.Empty;

        /// <summary>
        /// 银行账号
        /// </summary>
        public string AccountNumber { get; set; } = string.Empty;

        /// <summary>
        /// 账户类型
        /// </summary>
        public string? AccountType { get; set; }

        /// <summary>
        /// 是否默认账户
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
} 
# 百度地图问题解决方案

## 问题描述

在迁移到百度地图后，遇到了以下问题：
1. 地图无法正常加载
2. API密钥配置问题
3. 地图组件初始化失败

## 解决方案

### 1. API密钥配置

已配置百度地图API密钥：`MqycZpQuzhz8JuMH40W3yfVImAYcJ1g9`

**配置方式：**
- 在 `agricultureorderui/.env.local` 文件中设置：
  ```
  VITE_BAIDU_API_KEY=MqycZpQuzhz8JuMH40W3yfVImAYcJ1g9
  ```

**代码中的备用配置：**
- 在 `BaiduMap.vue` 和 `mapService.ts` 中都设置了备用API密钥
- 确保即使环境变量未配置也能正常工作

### 2. 地图API加载优化

**问题：** 百度地图API加载不稳定

**解决方案：**
- 使用回调函数方式加载API
- 添加超时处理机制
- 增加加载状态检查

```javascript
// 设置全局回调函数
window.initBaiduMap = () => {
  if (window.BMap) {
    resolve(window.BMap)
  } else {
    reject(new Error('百度地图API加载失败'))
  }
}

// 设置超时处理
const timeout = setTimeout(() => {
  reject(new Error('百度地图API加载超时'))
}, 10000)

// 监听加载完成
const checkLoad = () => {
  if (window.BMap) {
    clearTimeout(timeout)
    resolve(window.BMap)
  } else {
    setTimeout(checkLoad, 100)
  }
}
```

### 3. 组件接口保持兼容

**保持原有接口：**
- Props: `initialLocation`, `apiKey`
- Events: `locationSelected`, `locationCleared`
- Methods: `setLocation`

**新增功能：**
- 更好的错误处理
- 加载状态显示
- 重试机制

### 4. 测试页面

创建了专门的测试页面 `/baidu-map-test`，包含：
- 地图组件测试
- API服务测试
- 状态信息显示
- 实时错误反馈

## 使用方法

### 1. 访问测试页面
```
http://localhost:5173/baidu-map-test
```

### 2. 验证功能
- 地图是否正常加载
- 搜索功能是否正常
- 点击定位是否正常
- API密钥状态显示

### 3. 在表单中使用
客户和供应商表单中的"地图定位"按钮现在使用百度地图：
- 点击"地图定位"按钮
- 在弹出的对话框中搜索或点击选择位置
- 确认位置后自动填充坐标

## 技术细节

### API端点
- **地理编码**: `https://api.map.baidu.com/geocoding/v3/`
- **逆地理编码**: `https://api.map.baidu.com/reverse_geocoding/v3/`
- **JavaScript API**: `https://api.map.baidu.com/api?v=3.0&ak=...`

### 坐标系统
- 百度地图使用BD09坐标系
- 与WGS84坐标系有差异，但API会自动处理

### 错误处理
- 网络错误自动重试
- API调用失败友好提示
- 加载超时处理
- 模拟模式备用方案

## 常见问题

### Q: 地图无法加载怎么办？
A: 
1. 检查网络连接
2. 确认API密钥是否正确
3. 查看浏览器控制台错误信息
4. 尝试刷新页面

### Q: 搜索不准确怎么办？
A:
1. 使用更详细的地址信息
2. 确保地址包含城市信息
3. 尝试不同的地址表述方式

### Q: 如何获取当前位置？
A:
1. 点击"我的位置"按钮
2. 允许浏览器获取位置权限
3. 系统会自动定位到当前位置

## 下一步

1. 测试真实环境下的功能
2. 验证所有表单的地图定位功能
3. 优化用户体验
4. 添加更多地图功能（如路线规划等）

## 文件清单

### 修改的文件
- `src/components/BaiduMap.vue` - 百度地图组件
- `src/services/mapService.ts` - 地图服务
- `src/views/BaiduMapTest.vue` - 测试页面
- `src/router/index.ts` - 路由配置
- `.env.local` - 环境变量配置

### 新增的文件
- `BAIDU_MAP_FIX.md` - 问题解决文档
- `BAIDU_MAP_MIGRATION.md` - 迁移总结文档

### 删除的文件
- `src/components/AmapMap.vue` - 旧的高德地图组件 
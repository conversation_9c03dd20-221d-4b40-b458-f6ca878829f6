# 地图功能快速测试指南

## 🚀 立即开始测试

### 1. 启动开发服务器
```bash
cd agricultureorderui
npm run dev
```

### 2. 访问测试页面
打开浏览器访问：`http://localhost:5173/map-test`

### 3. 测试搜索功能
在搜索框中输入以下地址进行测试：

#### 真实API测试（需要网络连接）
- `北京`
- `上海`
- `广州`
- `深圳`
- `杭州`

#### 模拟数据测试（离线可用）
- `清华大学`
- `天安门`
- `外滩`
- `广州塔`
- `西湖`

## 🔍 功能验证

### API状态检查
- 查看页面顶部的"API状态"区域
- 确认API密钥是否正确显示
- 检查API状态是否为"正常"

### 搜索测试
1. 输入地址关键词
2. 点击搜索按钮
3. 查看搜索结果卡片
4. 观察地图上的标记位置

### 调试信息
- 查看页面底部的"调试信息"区域
- 观察控制台日志输出
- 检查API调用状态

## 🐛 问题排查

### 如果搜索显示模拟数据
1. 检查网络连接
2. 查看调试日志中的错误信息
3. 确认API密钥是否有效

### 如果地图无法加载
1. 检查浏览器控制台错误
2. 确认百度地图API是否可访问
3. 尝试刷新页面

### 如果搜索无结果
1. 尝试不同的关键词
2. 检查输入格式是否正确
3. 查看错误日志信息

## 📱 移动端测试

地图组件支持移动端使用：
- 触摸缩放和拖拽
- 响应式布局
- 移动端友好的搜索界面

## 🎯 预期结果

### 成功情况
- 搜索结果显示正确的地址和坐标
- 地图上显示对应的标记
- 调试日志显示API调用成功

### 回退情况
- 网络异常时自动使用模拟数据
- 显示友好的错误提示
- 保持基本功能可用

## 📞 技术支持

如果遇到问题，请：
1. 查看调试日志获取详细信息
2. 检查网络连接状态
3. 确认API密钥配置正确
4. 尝试使用不同的测试地址

---

**注意：** 首次使用可能需要等待几秒钟加载地图资源，请耐心等待。 
﻿using Agriculture.API.Read.Application.command;
using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using Agriculture.Infrastuctrue;
using MediatR;

namespace Agriculture.API.Read.Application.Handler
{
    public class SupplierQueryHandler : IRequestHandler<SupplierQuerycommand, APIResult<APIPageing<Supplier>>>
    {
        private readonly IBaseRepository<Supplier> sup;

        public SupplierQueryHandler(IBaseRepository<Supplier> sup)
        {
            this.sup = sup;
        }

        public Task<APIResult<APIPageing<Supplier>>> Handle(SupplierQuerycommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<APIPageing<Supplier>>();
            
            try
            {
                result.Code = ResultCode.Success;
                result.Message = "查询成功";
                
                var query = sup.GetAll();

                // 应用查询条件
                if (!string.IsNullOrEmpty(request.SupplierNumber))
                {
                    query = query.Where(s => s.SupplierNumber.Contains(request.SupplierNumber));
                }

                if (!string.IsNullOrEmpty(request.SupplierName))
                {
                    query = query.Where(s => s.SupplierName.Contains(request.SupplierName));
                }

                if (!string.IsNullOrEmpty(request.SupplierType))
                {
                    query = query.Where(s => s.SupplierType == request.SupplierType);
                }

                if (request.UsageStatus.HasValue)
                {
                    query = query.Where(s => s.UsageStatus == request.UsageStatus.Value);
                }

                // 获取总数
                var totalCount = query.Count();

                // 分页查询
                var pageData = query
                    .OrderByDescending(s => s.Id)
                    .Skip((request.PageIndex - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // 计算总页数
                var totalPage = (int)Math.Ceiling(totalCount * 1.0 / request.PageSize);

                // 构建分页结果
                var page = new APIPageing<Supplier>
                {
                    TotalCount = totalCount,
                    PageCount = totalPage,
                    PageData = pageData
                };

                result.Data = page;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"查询供应商失败：{ex.Message}";
            }

            return Task.FromResult(result);
        }
    }
}

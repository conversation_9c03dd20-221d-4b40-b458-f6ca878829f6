using Agriculture.API.Read.Application.command;
using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using Agriculture.Infrastuctrue;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace Agriculture.API.Read.Application.Handler
{
    /// <summary>
    /// 供应商详情查询处理器
    /// </summary>
    public class SupplierDetailHandler : IRequestHandler<SupplierDetailCommand, APIResult<Supplier>>
    {
        private readonly MyDbcontext _context;

        public SupplierDetailHandler(MyDbcontext context)
        {
            _context = context;
        }

        public async Task<APIResult<Supplier>> Handle(SupplierDetailCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<Supplier>();

            try
            {
                // 根据ID查询供应商，包含导航属性
                var supplier = await _context.Suppliers
                    .Include(s => s.BankAccounts)
                    .Include(s => s.Contacts)
                    .Include(s => s.ServicePersons)
                    .Include(s => s.ProductPrices)
                    .Include(s => s.Attachments)
                    .Include(s => s.AssociatedAccount)
                    .FirstOrDefaultAsync(s => s.Id == request.Id);

                // 添加详细调试日志
                if (supplier != null)
                {
                    Console.WriteLine($"=== 供应商详情调试信息 ===");
                    Console.WriteLine($"供应商ID: {supplier.Id}, 名称: {supplier.SupplierName}");
                    Console.WriteLine($"银行账户数量: {supplier.BankAccounts?.Count ?? 0}");
                    Console.WriteLine($"联系信息数量: {supplier.Contacts?.Count ?? 0}");
                    Console.WriteLine($"服务人员数量: {supplier.ServicePersons?.Count ?? 0}");
                    Console.WriteLine($"产品价格数量: {supplier.ProductPrices?.Count ?? 0}");
                    Console.WriteLine($"附件数量: {supplier.Attachments?.Count ?? 0}");
                    
                    // 检查银行账户详情
                    if (supplier.BankAccounts != null && supplier.BankAccounts.Any())
                    {
                        foreach (var account in supplier.BankAccounts)
                        {
                            Console.WriteLine($"  银行账户: {account.BankName} - {account.AccountNumber}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("  银行账户列表为空或null");
                    }
                    
                    // 检查联系信息详情
                    if (supplier.Contacts != null && supplier.Contacts.Any())
                    {
                        foreach (var contact in supplier.Contacts)
                        {
                            Console.WriteLine($"  联系人: {contact.ContactName} - {contact.ContactPhone}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("  联系信息列表为空或null");
                    }
                    
                    // 检查服务人员详情
                    if (supplier.ServicePersons != null && supplier.ServicePersons.Any())
                    {
                        foreach (var person in supplier.ServicePersons)
                        {
                            Console.WriteLine($"  服务人员: {person.SalespersonName} - {person.AffiliatedMarket}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("  服务人员列表为空或null");
                    }
                    
                    Console.WriteLine($"=== 调试信息结束 ===");
                }

                if (supplier == null)
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "供应商不存在";
                    return result;
                }

                result.Code = ResultCode.Success;
                result.Message = "获取供应商详情成功";
                result.Data = supplier;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取供应商详情失败：{ex.Message}";
            }

            return result;
        }
    }
} 
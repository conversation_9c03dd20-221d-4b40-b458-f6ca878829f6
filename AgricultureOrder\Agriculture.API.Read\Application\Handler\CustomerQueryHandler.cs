using Agriculture.API.Read.Application.command;
using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using Agriculture.Infrastuctrue;
using MediatR;

namespace Agriculture.API.Read.Application.Handler
{
    /// <summary>
    /// 客户查询处理器
    /// </summary>
    public class CustomerQueryHandler : IRequestHandler<CustomerQueryCommand, APIResult<APIPageing<Customer>>>
    {
        private readonly IBaseRepository<Customer> _customerRepository;

        public CustomerQueryHandler(IBaseRepository<Customer> customerRepository)
        {
            _customerRepository = customerRepository;
        }

        public async Task<APIResult<APIPageing<Customer>>> Handle(CustomerQueryCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<APIPageing<Customer>>();

            try
            {
                result.Code = ResultCode.Success;
                result.Message = "查询成功";

                var query = _customerRepository.GetAll();

                // 应用查询条件
                if (!string.IsNullOrEmpty(request.CustomerName))
                {
                    query = query.Where(c => c.CustomerName.Contains(request.CustomerName));
                }

                if (!string.IsNullOrEmpty(request.CustomerNumber))
                {
                    query = query.Where(c => c.CustomerNumber.Contains(request.CustomerNumber));
                }

                if (!string.IsNullOrEmpty(request.CustomerType))
                {
                    query = query.Where(c => c.CustomerType == request.CustomerType);
                }

                if (!string.IsNullOrEmpty(request.CustomerLevel))
                {
                    query = query.Where(c => c.CustomerLevel == request.CustomerLevel);
                }

                if (!string.IsNullOrEmpty(request.UsageStatus))
                {
                    query = query.Where(c => c.UsageStatus == request.UsageStatus);
                }

                // 获取总数
                var totalCount = query.Count();

                // 分页查询
                var pageData = query
                    .OrderByDescending(c => c.Id)
                    .Skip((request.PageIndex - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // 计算总页数
                var totalPage = (int)Math.Ceiling(totalCount * 1.0 / request.PageSize);

                // 构建分页结果
                var pageResult = new APIPageing<Customer>
                {
                    TotalCount = totalCount,
                    PageCount = totalPage,
                    PageData = pageData
                };

                result.Data = pageResult;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"查询客户失败：{ex.Message}";
            }

            return result;
        }
    }
} 
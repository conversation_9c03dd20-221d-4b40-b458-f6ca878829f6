using Agriculture.Domain.Entities;
using Agriculture.Infrastuctrue;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Agriculture.API.Read.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly MyDbcontext _context;

        public TestController(MyDbcontext context)
        {
            _context = context;
        }

        [HttpGet("supplier/{id}")]
        public async Task<IActionResult> TestSupplierDetail(long id)
        {
            try
            {
                // 测试1: 直接查询供应商基本信息
                var supplierBasic = await _context.Suppliers
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (supplierBasic == null)
                {
                    return NotFound($"供应商ID {id} 不存在");
                }

                // 测试2: 查询关联数据数量
                var bankAccountCount = await _context.SupplierBankAccounts
                    .CountAsync(sba => sba.SupplierId == id);

                var contactCount = await _context.SupplierContacts
                    .CountAsync(sc => sc.SupplierId == id);

                var servicePersonCount = await _context.SupplierServicePersons
                    .CountAsync(ssp => ssp.SupplierId == id);

                // 测试3: 使用Include查询完整数据
                var supplierWithIncludes = await _context.Suppliers
                    .Include(s => s.BankAccounts)
                    .Include(s => s.Contacts)
                    .Include(s => s.ServicePersons)
                    .Include(s => s.ProductPrices)
                    .Include(s => s.Attachments)
                    .FirstOrDefaultAsync(s => s.Id == id);

                var result = new
                {
                    SupplierBasic = new
                    {
                        Id = supplierBasic.Id,
                        Name = supplierBasic.SupplierName,
                        Number = supplierBasic.SupplierNumber
                    },
                    DirectQueryCounts = new
                    {
                        BankAccounts = bankAccountCount,
                        Contacts = contactCount,
                        ServicePersons = servicePersonCount
                    },
                    IncludeQueryCounts = new
                    {
                        BankAccounts = supplierWithIncludes?.BankAccounts?.Count ?? 0,
                        Contacts = supplierWithIncludes?.Contacts?.Count ?? 0,
                        ServicePersons = supplierWithIncludes?.ServicePersons?.Count ?? 0,
                        ProductPrices = supplierWithIncludes?.ProductPrices?.Count ?? 0,
                        Attachments = supplierWithIncludes?.Attachments?.Count ?? 0
                    },
                    BankAccounts = supplierWithIncludes?.BankAccounts,
                    Contacts = supplierWithIncludes?.Contacts,
                    ServicePersons = supplierWithIncludes?.ServicePersons
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        [HttpGet("database-info")]
        public async Task<IActionResult> GetDatabaseInfo()
        {
            try
            {
                var supplierCount = await _context.Suppliers.CountAsync();
                var bankAccountCount = await _context.SupplierBankAccounts.CountAsync();
                var contactCount = await _context.SupplierContacts.CountAsync();
                var servicePersonCount = await _context.SupplierServicePersons.CountAsync();

                var result = new
                {
                    TotalSuppliers = supplierCount,
                    TotalBankAccounts = bankAccountCount,
                    TotalContacts = contactCount,
                    TotalServicePersons = servicePersonCount,
                    ConnectionString = _context.Database.GetConnectionString()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
} 
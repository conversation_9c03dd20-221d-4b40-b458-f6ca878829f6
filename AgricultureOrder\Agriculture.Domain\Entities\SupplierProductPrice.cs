using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 供应商商品采购价格
    /// </summary>
    public class SupplierProductPrice : BaseEntity
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public long SupplierId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public virtual Supplier Supplier { get; set; } = null!;

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 商品编码
        /// </summary>
        public string? ProductCode { get; set; }

        /// <summary>
        /// 商品规格
        /// </summary>
        public string? ProductSpecification { get; set; }

        /// <summary>
        /// 采购价格
        /// </summary>
        public decimal PurchasePrice { get; set; }

        /// <summary>
        /// 货币单位
        /// </summary>
        public string Currency { get; set; } = "CNY";

        /// <summary>
        /// 价格生效时间
        /// </summary>
        public DateTime? PriceEffectiveTime { get; set; }

        /// <summary>
        /// 价格失效时间
        /// </summary>
        public DateTime? PriceExpirationTime { get; set; }

        /// <summary>
        /// 数量单位
        /// </summary>
        public string? QuantityUnit { get; set; }
    }
} 
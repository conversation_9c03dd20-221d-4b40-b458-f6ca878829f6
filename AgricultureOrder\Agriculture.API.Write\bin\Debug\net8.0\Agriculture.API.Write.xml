<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Agriculture.API.Write</name>
    </assembly>
    <members>
        <member name="T:Agriculture.API.Write.Application.command.CustomerAddCommand">
            <summary>
            客户添加命令
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.CustomerNumber">
            <summary>
            客户编号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.CustomerName">
            <summary>
            客户名称
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.CustomerType">
            <summary>
            客户类型（个人/企业）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.PersonInChargeName">
            <summary>
            负责人姓名
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.PersonInChargePhone">
            <summary>
            负责人手机号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.IdCard">
            <summary>
            身份证号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.Location">
            <summary>
            所在地
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.Industry">
            <summary>
            所属行业
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.UnifiedSocialCreditCode">
            <summary>
            统一社会信用代码（税号）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.MnemonicCode">
            <summary>
            助记码
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.CustomerStage">
            <summary>
            客户阶段
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.CustomTags">
            <summary>
            客户自定义标签
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.SuperiorCustomerId">
            <summary>
            上级客户ID
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.UsageStatus">
            <summary>
            使用状态（启用/停用）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.CustomerLevel">
            <summary>
            客户级别
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.CustomerGroupClassification">
            <summary>
            客户集团分类
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.EntryUnit">
            <summary>
            录入单位
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.UsageUnit">
            <summary>
            使用单位
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.IsArchived">
            <summary>
            归集档案
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.Longitude">
            <summary>
            地图定位经度
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.CustomerAddCommand.Latitude">
            <summary>
            地图定位纬度
            </summary>
        </member>
        <member name="T:Agriculture.API.Write.Application.command.SupplierAddCommand">
            <summary>
            供应商添加命令
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.SupplierNumber">
            <summary>
            供应商编号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.SupplierName">
            <summary>
            供应商名称
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.SupplierType">
            <summary>
            供应商类型（个人/企业）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.PersonInChargeName">
            <summary>
            负责人姓名
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.PersonInChargePhone">
            <summary>
            负责人手机号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.IdCard">
            <summary>
            身份证号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.Location">
            <summary>
            所在地
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.Industry">
            <summary>
            所属行业
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.UnifiedSocialCreditCode">
            <summary>
            统一社会信用代码（税号）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.MnemonicCode">
            <summary>
            助记码
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.UsageStatus">
            <summary>
            使用状态（启用/停用）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.EntryUnit">
            <summary>
            录入单位
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.UsageUnit">
            <summary>
            使用单位
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.IsArchived">
            <summary>
            归集档案
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.Longitude">
            <summary>
            地图定位经度
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.command.SupplierAddCommand.Latitude">
            <summary>
            地图定位纬度
            </summary>
        </member>
        <member name="T:Agriculture.API.Write.Application.Handler.CustomerAddHandler">
            <summary>
            客户添加处理器
            </summary>
        </member>
        <member name="T:Agriculture.API.Write.Application.Handler.SupplierAddHandler">
            <summary>
            供应商添加处理器
            </summary>
        </member>
        <member name="T:Agriculture.API.Write.Application.Models.CustomerDto">
            <summary>
            客户数据传输对象
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.CustomerNumber">
            <summary>
            客户编号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.CustomerName">
            <summary>
            客户名称
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.CustomerType">
            <summary>
            客户类型（个人/企业）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.PersonInChargeName">
            <summary>
            负责人姓名
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.PersonInChargePhone">
            <summary>
            负责人手机号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.IdCard">
            <summary>
            身份证号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.Location">
            <summary>
            所在地
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.Industry">
            <summary>
            所属行业
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.UnifiedSocialCreditCode">
            <summary>
            统一社会信用代码（税号）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.MnemonicCode">
            <summary>
            助记码
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.CustomerStage">
            <summary>
            客户阶段
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.CustomTags">
            <summary>
            客户自定义标签
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.SuperiorCustomerId">
            <summary>
            上级客户ID
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.UsageStatus">
            <summary>
            使用状态（启用/停用）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.CustomerLevel">
            <summary>
            客户级别
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.CustomerGroupClassification">
            <summary>
            客户集团分类
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.EntryUnit">
            <summary>
            录入单位
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.UsageUnit">
            <summary>
            使用单位
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.IsArchived">
            <summary>
            归集档案
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.Longitude">
            <summary>
            地图定位经度
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.CustomerDto.Latitude">
            <summary>
            地图定位纬度
            </summary>
        </member>
        <member name="T:Agriculture.API.Write.Application.Models.SupplierDto">
            <summary>
            供应商数据传输对象
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.SupplierNumber">
            <summary>
            供应商编号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.SupplierName">
            <summary>
            供应商名称
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.SupplierType">
            <summary>
            供应商类型（个人/企业）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.PersonInChargeName">
            <summary>
            负责人姓名
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.PersonInChargePhone">
            <summary>
            负责人手机号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.IdCard">
            <summary>
            身份证号
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.Location">
            <summary>
            所在地
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.Industry">
            <summary>
            所属行业
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.UnifiedSocialCreditCode">
            <summary>
            统一社会信用代码（税号）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.MnemonicCode">
            <summary>
            助记码
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.UsageStatus">
            <summary>
            使用状态（启用/停用）
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.EntryUnit">
            <summary>
            录入单位
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.UsageUnit">
            <summary>
            使用单位
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.IsArchived">
            <summary>
            归集档案
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.Longitude">
            <summary>
            地图定位经度
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.Application.Models.SupplierDto.Latitude">
            <summary>
            地图定位纬度
            </summary>
        </member>
        <member name="T:Agriculture.API.Write.Controllers.ManagementController">
            <summary>
            管理控制器
            </summary>
        </member>
        <member name="M:Agriculture.API.Write.Controllers.ManagementController.AddSupplier(Agriculture.API.Write.Application.command.SupplierAddCommand)">
            <summary>
            添加供应商
            </summary>
            <param name="command">供应商添加命令</param>
            <returns>添加结果</returns>
        </member>
        <member name="M:Agriculture.API.Write.Controllers.ManagementController.AddCustomer(Agriculture.API.Write.Application.command.CustomerAddCommand)">
            <summary>
            添加客户
            </summary>
            <param name="command">客户添加命令</param>
            <returns>添加结果</returns>
        </member>
        <member name="T:Agriculture.API.Write.WeatherForecast">
            <summary>
            天气预报模型
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.WeatherForecast.Date">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.WeatherForecast.TemperatureC">
            <summary>
            摄氏温度
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.WeatherForecast.TemperatureF">
            <summary>
            华氏温度
            </summary>
        </member>
        <member name="P:Agriculture.API.Write.WeatherForecast.Summary">
            <summary>
            天气摘要
            </summary>
        </member>
    </members>
</doc>

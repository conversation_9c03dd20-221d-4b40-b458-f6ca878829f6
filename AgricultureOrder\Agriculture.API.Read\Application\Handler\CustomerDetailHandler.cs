using Agriculture.API.Read.Application.command;
using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using Agriculture.Infrastuctrue;
using MediatR;

namespace Agriculture.API.Read.Application.Handler
{
    /// <summary>
    /// 客户详情查询处理器
    /// </summary>
    public class CustomerDetailHandler : IRequestHandler<CustomerDetailCommand, APIResult<Customer>>
    {
        private readonly IBaseRepository<Customer> _customerRepository;

        public CustomerDetailHandler(IBaseRepository<Customer> customerRepository)
        {
            _customerRepository = customerRepository;
        }

        public async Task<APIResult<Customer>> Handle(CustomerDetailCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<Customer>();

            try
            {
                // 根据ID查询客户
                var customer = await _customerRepository.GetModel(request.Id);

                if (customer == null)
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "客户不存在";
                    return result;
                }

                result.Code = ResultCode.Success;
                result.Message = "获取客户详情成功";
                result.Data = customer;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取客户详情失败：{ex.Message}";
            }

            return result;
        }
    }
} 
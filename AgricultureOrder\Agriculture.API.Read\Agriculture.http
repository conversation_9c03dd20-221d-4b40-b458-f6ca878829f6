@Agriculture_HostAddress = http://localhost:5102

### 获取供应商列表
GET {{Agriculture_HostAddress}}/api/Management/GetSupplier?SupplierName=测试&PageIndex=1&PageSize=10

### 获取供应商详情（反填功能）
GET {{Agriculture_HostAddress}}/api/Management/GetSupplierDetail?id=1

### 获取客户列表
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?CustomerName=测试&PageIndex=1&PageSize=10

### 获取客户详情（反填功能）
GET {{Agriculture_HostAddress}}/api/Management/GetCustomerDetail?id=1

### 按客户类型查询
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?CustomerType=企业&PageIndex=1&PageSize=10

### 按客户级别查询
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?CustomerLevel=A级&PageIndex=1&PageSize=10

### 按使用状态查询
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?UsageStatus=启用&PageIndex=1&PageSize=10

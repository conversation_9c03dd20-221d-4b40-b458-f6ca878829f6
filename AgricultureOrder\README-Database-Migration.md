# 数据库迁移指南

## 概述

本项目使用 Entity Framework Core 8.0 和 MySQL 数据库。本文档将指导您如何设置和运行数据库迁移。

## 前置条件

1. 安装 .NET 8.0 SDK
2. 安装 MySQL 数据库服务器
3. 确保 MySQL 服务正在运行

## 数据库配置

### 1. 连接字符串配置

在 `Agriculture.API.Write/appsettings.json` 文件中配置数据库连接字符串：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=AgricultureOrder;User=root;Password=******;CharSet=utf8mb4;"
  }
}
```

请根据您的实际数据库配置修改连接字符串。

### 2. 数据库结构

项目包含以下主要实体：

#### 客户管理
- `Customer` - 客户基本信息
- `CustomerAccount` - 客户账户信息
- `CustomerContact` - 客户联系人
- `CustomerServicePerson` - 客户服务人员
- `CustomerBankAccount` - 客户银行账户
- `CustomerProductSolution` - 客户产品方案
- `CustomerDiscountSolution` - 客户折扣方案
- `CustomerAttachment` - 客户附件

#### 供应商管理
- `Supplier` - 供应商基本信息
- `SupplierAccount` - 供应商账户信息
- `SupplierContact` - 供应商联系人
- `SupplierServicePerson` - 供应商服务人员
- `SupplierBankAccount` - 供应商银行账户
- `SupplierProductPrice` - 供应商产品价格
- `SupplierAttachment` - 供应商附件

## 迁移命令

### 方法一：使用 PowerShell 脚本（推荐）

1. 打开 PowerShell
2. 导航到项目根目录
3. 执行迁移脚本：

```powershell
.\migrate-database.ps1
```

### 方法二：手动执行命令

#### 1. 创建初始迁移

```bash
dotnet ef migrations add InitialCreate --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write
```

#### 2. 应用迁移到数据库

```bash
dotnet ef database update --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write
```

#### 3. 查看迁移列表

```bash
dotnet ef migrations list --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write
```

#### 4. 生成 SQL 脚本（可选）

```bash
dotnet ef migrations script --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write
```

#### 5. 移除最后一个迁移（如果需要）

```bash
dotnet ef migrations remove --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write
```

## 开发工作流

### 1. 修改实体模型

当您修改 Domain 层中的实体类时，需要创建新的迁移：

```bash
dotnet ef migrations add [迁移名称] --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write
```

### 2. 应用迁移

```bash
dotnet ef database update --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write
```

### 3. 验证迁移

启动应用程序，检查数据库是否正确创建和更新。

## 故障排除

### 1. 连接字符串错误

确保 MySQL 服务正在运行，并且连接字符串中的用户名、密码和数据库名称正确。

### 2. 权限问题

确保数据库用户具有创建数据库和表的权限。

### 3. 迁移冲突

如果遇到迁移冲突，可以：

1. 删除 `Migrations` 文件夹中的所有文件
2. 删除数据库
3. 重新创建初始迁移

```bash
# 删除迁移文件后
dotnet ef migrations add InitialCreate --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write
dotnet ef database update --project Agriculture.Infrastuctrue --startup-project Agriculture.API.Write
```

## 生产环境部署

在生产环境中，建议：

1. 使用环境变量或配置文件管理连接字符串
2. 在部署前测试迁移脚本
3. 备份现有数据库
4. 在维护窗口期间执行迁移

## 注意事项

1. 所有实体都继承自 `BaseEntity`，包含审计字段
2. 软删除使用 `IsDeleted` 字段实现
3. 外键关系配置了适当的删除行为
4. 字符串字段都有最大长度限制
5. 数值字段使用适当的精度配置

## 支持

如果遇到问题，请检查：

1. Entity Framework Core 工具是否正确安装
2. 项目引用是否正确配置
3. 数据库连接是否正常
4. 实体配置是否有语法错误 
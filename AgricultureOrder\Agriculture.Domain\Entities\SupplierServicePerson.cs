using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 供应商服务人员
    /// </summary>
    public class SupplierServicePerson : BaseEntity
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public long SupplierId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public virtual Supplier Supplier { get; set; } = null!;

        /// <summary>
        /// 所属市场
        /// </summary>
        public string? AffiliatedMarket { get; set; }

        /// <summary>
        /// 业务员姓名
        /// </summary>
        public string? SalespersonName { get; set; }

        /// <summary>
        /// 业务员ID
        /// </summary>
        public long? SalespersonId { get; set; }
    }
} 
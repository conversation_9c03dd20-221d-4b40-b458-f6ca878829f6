-- 插入供应商银行账户测试数据
INSERT INTO SupplierBankAccounts (
    SupplierId,
    BankName,
    AccountNumber,
    AccountName,
    IsDefault,
    CreateTime,
    UpdateTime,
    IsDeleted,
    CreateUserId,
    UpdateUserId
) VALUES 
(1, '中国工商银行', '6222021234567890123', '北京农业科技有限公司', true, NOW(), NOW(), false, 1, 1),
(1, '中国建设银行', '6227001234567890123', '北京农业科技有限公司', false, NOW(), NOW(), false, 1, 1),
(2, '中国农业银行', '6228481234567890123', '上海农产品供应商', true, NOW(), NOW(), false, 1, 1),
(3, '中国银行', '6013821234567890123', '广州种子公司', true, NOW(), NOW(), false, 1, 1),
(4, '招商银行', '6212861234567890123', '深圳农药供应商', true, NOW(), NOW(), false, 1, 1),
(5, '交通银行', '6222601234567890123', '杭州农机设备公司', true, NOW(), NOW(), false, 1, 1);

-- 插入供应商联系信息测试数据
INSERT INTO SupplierContacts (
    SupplierId,
    ContactName,
    ContactPhone,
    ContactEmail,
    ContactAddress,
    Position,
    IsPrimary,
    CreateTime,
    UpdateTime,
    IsDeleted,
    CreateUserId,
    UpdateUserId
) VALUES 
(1, '张三', '13800138001', '<EMAIL>', '北京市朝阳区农业科技园A座', '总经理', true, NOW(), NOW(), false, 1, 1),
(1, '李经理', '13800138011', '<EMAIL>', '北京市朝阳区农业科技园B座', '销售经理', false, NOW(), NOW(), false, 1, 1),
(2, '李四', '13800138002', '<EMAIL>', '上海市浦东新区农产品市场', '负责人', true, NOW(), NOW(), false, 1, 1),
(3, '王五', '13800138003', '<EMAIL>', '广州市天河区种子市场', '总经理', true, NOW(), NOW(), false, 1, 1),
(4, '赵六', '13800138004', '<EMAIL>', '深圳市南山区农药市场', '销售总监', true, NOW(), NOW(), false, 1, 1),
(5, '钱七', '13800138005', '<EMAIL>', '杭州市西湖区农机市场', '技术总监', true, NOW(), NOW(), false, 1, 1);

-- 插入供应商服务人员测试数据
INSERT INTO SupplierServicePersons (
    SupplierId,
    AffiliatedMarket,
    SalespersonName,
    SalespersonId,
    CreateTime,
    UpdateTime,
    IsDeleted,
    CreateUserId,
    UpdateUserId
) VALUES 
(1, '北京农业科技市场', '张业务员', 'SP001', NOW(), NOW(), false, 1, 1),
(1, '北京农产品批发市场', '李业务员', 'SP002', NOW(), NOW(), false, 1, 1),
(2, '上海农产品市场', '王业务员', 'SP003', NOW(), NOW(), false, 1, 1),
(3, '广州种子市场', '赵业务员', 'SP004', NOW(), NOW(), false, 1, 1),
(4, '深圳农药市场', '钱业务员', 'SP005', NOW(), NOW(), false, 1, 1),
(5, '杭州农机市场', '孙业务员', 'SP006', NOW(), NOW(), false, 1, 1);

-- 插入供应商附件测试数据
INSERT INTO SupplierAttachments (
    SupplierId,
    AttachmentName,
    AttachmentType,
    FilePath,
    FileSize,
    FileExtension,
    CreateTime,
    UpdateTime,
    IsDeleted,
    CreateUserId,
    UpdateUserId
) VALUES 
(1, '营业执照', '营业执照', '/attachments/supplier1/license.pdf', 1024000, 'pdf', NOW(), NOW(), false, 1, 1),
(1, '税务登记证', '税务登记证', '/attachments/supplier1/tax.pdf', 512000, 'pdf', NOW(), NOW(), false, 1, 1),
(2, '企业资质证书', '资质证书', '/attachments/supplier2/certificate.pdf', 2048000, 'pdf', NOW(), NOW(), false, 1, 1),
(3, '种子经营许可证', '经营许可证', '/attachments/supplier3/seed_license.pdf', 1536000, 'pdf', NOW(), NOW(), false, 1, 1),
(4, '农药经营许可证', '经营许可证', '/attachments/supplier4/pesticide_license.pdf', 1792000, 'pdf', NOW(), NOW(), false, 1, 1),
(5, '农机设备证书', '设备证书', '/attachments/supplier5/equipment_cert.pdf', 2560000, 'pdf', NOW(), NOW(), false, 1, 1);

-- 插入供应商产品价格测试数据
INSERT INTO SupplierProductPrices (
    SupplierId,
    ProductName,
    ProductCode,
    UnitPrice,
    Currency,
    EffectiveDate,
    ExpiryDate,
    CreateTime,
    UpdateTime,
    IsDeleted,
    CreateUserId,
    UpdateUserId
) VALUES 
(1, '优质玉米种子', 'CORN001', 25.50, 'CNY', '2025-01-01', '2025-12-31', NOW(), NOW(), false, 1, 1),
(1, '小麦种子', 'WHEAT001', 18.80, 'CNY', '2025-01-01', '2025-12-31', NOW(), NOW(), false, 1, 1),
(2, '新鲜蔬菜', 'VEG001', 3.20, 'CNY', '2025-01-01', '2025-12-31', NOW(), NOW(), false, 1, 1),
(3, '杂交水稻种子', 'RICE001', 35.00, 'CNY', '2025-01-01', '2025-12-31', NOW(), NOW(), false, 1, 1),
(4, '有机农药', 'PEST001', 45.60, 'CNY', '2025-01-01', '2025-12-31', NOW(), NOW(), false, 1, 1),
(5, '小型拖拉机', 'TRACTOR001', 15000.00, 'CNY', '2025-01-01', '2025-12-31', NOW(), NOW(), false, 1, 1); 
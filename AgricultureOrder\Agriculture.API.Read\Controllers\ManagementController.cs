﻿using Agriculture.API.Read.Application.command;
using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Agriculture.API.Read.Controllers
{
    /// <summary>
    /// 管理控制器
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ManagementController : ControllerBase
    {
        private readonly IMediator mediator;

        public ManagementController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        
        /// <summary>
        /// 获取供应商列表
        /// </summary>
        /// <param name="request">命令</param>
        /// <returns>返回任务</returns>
        [HttpGet]
        public Task<APIResult<APIPageing<Supplier>>> GetSupplier([FromQuery]SupplierQuerycommand request)
        {
            return mediator.Send(request);
        }

        /// <summary>
        /// 获取供应商详情（反填功能）
        /// </summary>
        /// <param name="id">供应商ID</param>
        /// <returns>供应商详情</returns>
        [HttpGet]
        public async Task<APIResult<Supplier>> GetSupplierDetail([FromQuery] long id)
        {
            var command = new SupplierDetailCommand { Id = id };
            return await mediator.Send(command);
        }

        /// <summary>
        /// 获取客户列表
        /// </summary>
        /// <param name="request">查询命令</param>
        /// <returns>客户列表</returns>
        [HttpGet]
        public async Task<APIResult<APIPageing<Customer>>> GetCustomer([FromQuery] CustomerQueryCommand request)
        {
            return await mediator.Send(request);
        }

        /// <summary>
        /// 获取客户详情（反填功能）
        /// </summary>
        /// <param name="id">客户ID</param>
        /// <returns>客户详情</returns>
        [HttpGet]
        public async Task<APIResult<Customer>> GetCustomerDetail([FromQuery] long id)
        {
            var command = new CustomerDetailCommand { Id = id };
            return await mediator.Send(command);
        }
    }
}

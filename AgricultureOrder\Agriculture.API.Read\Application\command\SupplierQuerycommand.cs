﻿using Agriculture.Domain.Entities;
using Agriculture.ErrorCode;
using MediatR;

namespace Agriculture.API.Read.Application.command
{
    public class SupplierQuerycommand:IRequest<APIResult<APIPageing<Supplier>>>
    {
        /// <summary>
        /// 供应商编号
        /// </summary>
        public string? SupplierNumber { get; set; }
        
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? SupplierName { get; set; }
        
        /// <summary>
        /// 供应商类型
        /// </summary>
        public string? SupplierType { get; set; }
        
        /// <summary>
        /// 使用状态
        /// </summary>
        public bool? UsageStatus { get; set; }
        
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;
        
        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 5;
    }
}

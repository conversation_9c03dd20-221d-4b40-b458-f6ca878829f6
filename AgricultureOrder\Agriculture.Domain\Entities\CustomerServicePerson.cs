using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 客户服务人员
    /// </summary>
    public class CustomerServicePerson : BaseEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public virtual Customer Customer { get; set; } = null!;

        /// <summary>
        /// 所属市场
        /// </summary>
        public string? AffiliatedMarket { get; set; }

        /// <summary>
        /// 业务员姓名
        /// </summary>
        public string? SalespersonName { get; set; }

        /// <summary>
        /// 业务员ID
        /// </summary>
        public long? SalespersonId { get; set; }

        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime? ServiceStartTime { get; set; }

        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime? ServiceEndTime { get; set; }

        /// <summary>
        /// 服务状态
        /// </summary>
        public string? ServiceStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
} 
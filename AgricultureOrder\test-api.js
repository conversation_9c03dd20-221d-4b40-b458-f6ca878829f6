// 测试API调用
async function testSupplierAPI() {
  try {
    const response = await fetch('http://localhost:5102/api/Management/GetSupplier?PageIndex=1&PageSize=4');
    const data = await response.json();
    
    console.log('API Response:', JSON.stringify(data, null, 2));
    
    if (data.code === 200) {
      console.log('✅ API调用成功');
      console.log('📊 数据总数:', data.data.totalCount);
      console.log('📄 当前页数据:', data.data.pageData.length);
      
      if (data.data.pageData.length > 0) {
        console.log('📋 第一条数据:', data.data.pageData[0]);
      }
    } else {
      console.log('❌ API调用失败:', data.message);
    }
  } catch (error) {
    console.error('❌ 网络错误:', error);
  }
}

// 在浏览器控制台中运行
testSupplierAPI(); 
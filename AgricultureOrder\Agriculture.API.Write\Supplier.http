### 供应商管理API测试

### 添加供应商
POST {{baseUrl}}/api/Management/AddSupplier
Content-Type: application/json

{
  "supplierNumber": "SUP001",
  "supplierName": "测试供应商",
  "supplierType": "企业",
  "personInChargeName": "张三",
  "personInChargePhone": "13800138000",
  "idCard": "110101199001011234",
  "location": "北京市朝阳区",
  "industry": "农业",
  "unifiedSocialCreditCode": "91110105MA00123456",
  "mnemonicCode": "CSGYS",
  "usageStatus": true,
  "entryUnit": "系统管理员",
  "usageUnit": "采购部",
  "remarks": "测试供应商备注",
  "isArchived": false,
  "longitude": 116.4074,
  "latitude": 39.9042
}

### 添加个人供应商
POST {{baseUrl}}/api/Management/AddSupplier
Content-Type: application/json

{
  "supplierNumber": "SUP002",
  "supplierName": "个人供应商",
  "supplierType": "个人",
  "personInChargeName": "李四",
  "personInChargePhone": "13900139000",
  "idCard": "110101199002022345",
  "location": "上海市浦东新区",
  "industry": "农产品",
  "mnemonicCode": "GRGYS",
  "usageStatus": true,
  "entryUnit": "系统管理员",
  "usageUnit": "采购部",
  "remarks": "个人供应商备注"
}

### 客户管理API测试

### 添加企业客户
POST {{baseUrl}}/api/Management/AddCustomer
Content-Type: application/json

{
  "customerNumber": "CUS001",
  "customerName": "测试企业客户",
  "customerType": "企业",
  "personInChargeName": "王五",
  "personInChargePhone": "13700137000",
  "idCard": "110101199003033456",
  "location": "广州市天河区",
  "industry": "农业",
  "unifiedSocialCreditCode": "91440101MA00123456",
  "mnemonicCode": "CSKH",
  "customerStage": "潜在客户",
  "customTags": "重要客户,农业企业",
  "usageStatus": "启用",
  "customerLevel": "A级",
  "customerGroupClassification": "农业集团",
  "entryUnit": "系统管理员",
  "usageUnit": "销售部",
  "remarks": "测试企业客户备注",
  "isArchived": false,
  "longitude": 113.2644,
  "latitude": 23.1291
}

### 添加个人客户
POST {{baseUrl}}/api/Management/AddCustomer
Content-Type: application/json

{
  "customerNumber": "CUS002",
  "customerName": "个人客户",
  "customerType": "个人",
  "personInChargeName": "赵六",
  "personInChargePhone": "13600136000",
  "idCard": "110101199004044567",
  "location": "深圳市南山区",
  "industry": "农产品",
  "mnemonicCode": "GRKH",
  "customerStage": "意向客户",
  "customTags": "个人客户",
  "usageStatus": "启用",
  "customerLevel": "B级",
  "entryUnit": "系统管理员",
  "usageUnit": "销售部",
  "remarks": "个人客户备注"
} 
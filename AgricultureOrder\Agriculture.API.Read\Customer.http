### 客户查询API测试

@Agriculture_HostAddress = http://localhost:5102

### 获取客户列表（基础查询）
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?PageIndex=1&PageSize=10

### 按客户名称模糊查询
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?CustomerName=测试&PageIndex=1&PageSize=10

### 按客户编号查询
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?CustomerNumber=CUS&PageIndex=1&PageSize=10

### 按客户类型查询
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?CustomerType=企业&PageIndex=1&PageSize=10

### 按客户级别查询
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?CustomerLevel=A级&PageIndex=1&PageSize=10

### 按使用状态查询
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?UsageStatus=启用&PageIndex=1&PageSize=10

### 组合查询（企业类型 + A级客户）
GET {{Agriculture_HostAddress}}/api/Management/GetCustomer?CustomerType=企业&CustomerLevel=A级&PageIndex=1&PageSize=10

### 获取客户详情（反填功能）
GET {{Agriculture_HostAddress}}/api/Management/GetCustomerDetail?id=1

### 获取客户详情（不存在的ID）
GET {{Agriculture_HostAddress}}/api/Management/GetCustomerDetail?id=999 
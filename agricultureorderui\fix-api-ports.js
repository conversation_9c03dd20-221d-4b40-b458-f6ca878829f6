/**
 * API端口修复脚本
 * 用于解决前端API调用端口不匹配的问题
 * 
 * 使用方法：
 * 1. 在项目根目录下运行：node fix-api-ports.js
 * 2. 重启开发服务器
 */

const fs = require('fs');
const path = require('path');

// 文件路径
const apiConfigPath = path.join(__dirname, 'src', 'services', 'apiConfig.ts');

// 创建API配置文件
function createApiConfigFile() {
  console.log('创建API配置文件...');
  
  try {
    const configContent = `// API配置文件 - 处理读写API的不同端口

// API基础URL配置
export const API_CONFIG = {
  // 写API (POST, PUT, DELETE)
  WRITE_API_BASE_URL: import.meta.env.VITE_API_WRITE_BASE_URL || 'http://localhost:5214',
  
  // 读API (GET)
  READ_API_BASE_URL: import.meta.env.VITE_API_READ_BASE_URL || 'http://localhost:5102'
};

/**
 * 根据请求方法获取对应的API基础URL
 * @param method 请求方法
 * @returns 对应的API基础URL
 */
export function getApiBaseUrl(method: string): string {
  // 将方法转为大写以便比较
  const upperMethod = method.toUpperCase();
  
  // 如果是GET请求，使用读API的URL
  if (upperMethod === 'GET') {
    return API_CONFIG.READ_API_BASE_URL;
  }
  
  // 其他请求（POST, PUT, DELETE等）使用写API的URL
  return API_CONFIG.WRITE_API_BASE_URL;
}

export default API_CONFIG;`;

    fs.writeFileSync(apiConfigPath, configContent);
    console.log('API配置文件创建成功');
    return true;
  } catch (error) {
    console.error('创建API配置文件失败:', error);
    return false;
  }
}

// 修改API服务文件
function updateApiServiceFile() {
  console.log('修改API服务文件...');
  
  try {
    const apiServicePath = path.join(__dirname, 'src', 'services', 'api.ts');
    let apiServiceContent = fs.readFileSync(apiServicePath, 'utf8');
    
    // 替换API基础配置
    apiServiceContent = apiServiceContent.replace(
      /\/\/ API基础配置\s*const API_BASE_URL = [^\n]+/,
      '// 导入API配置\nimport { getApiBaseUrl } from \'./apiConfig\''
    );
    
    // 修改request函数
    apiServiceContent = apiServiceContent.replace(
      /const fullUrl = `\${API_BASE_URL}\${url}`/,
      '// 根据请求方法获取对应的API基础URL\n  const method = options.method || \'GET\'\n  const API_BASE_URL = getApiBaseUrl(method)\n  \n  const fullUrl = `${API_BASE_URL}${url}`\n  console.log(`发送${method}请求到: ${fullUrl}`)'
    );
    
    fs.writeFileSync(apiServicePath, apiServiceContent);
    console.log('API服务文件修改成功');
    return true;
  } catch (error) {
    console.error('修改API服务文件失败:', error);
    return false;
  }
}

// 创建环境变量文件
function createEnvFile() {
  console.log('创建环境变量文件...');
  
  try {
    const envPath = path.join(__dirname, '.env.local');
    const envContent = `# 百度地图API密钥
VITE_BAIDU_API_KEY=MqycZpQuzhz8JuMH40W3yfVImAYcJ1g9

# API基础URL配置
VITE_API_WRITE_BASE_URL=http://localhost:5214
VITE_API_READ_BASE_URL=http://localhost:5102
`;
    
    fs.writeFileSync(envPath, envContent);
    console.log('环境变量文件创建成功');
    return true;
  } catch (error) {
    console.error('创建环境变量文件失败:', error);
    return false;
  }
}

// 主函数
function main() {
  console.log('开始修复API端口问题...');
  
  const configResult = createApiConfigFile();
  const serviceResult = updateApiServiceFile();
  const envResult = createEnvFile();
  
  if (configResult && serviceResult && envResult) {
    console.log('\n✅ 修复成功！');
    console.log('\n请重启开发服务器以应用修复:');
    console.log('  npm run dev');
  } else {
    console.log('\n❌ 修复失败，请查看上述错误信息');
  }
}

// 执行主函数
main(); 
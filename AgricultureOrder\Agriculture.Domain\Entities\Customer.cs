using System.ComponentModel.DataAnnotations;

namespace Agriculture.Domain.Entities
{
    /// <summary>
    /// 客户实体
    /// </summary>
    public class Customer : BaseEntity
    {
        /// <summary>
        /// 客户编号
        /// </summary>
        public string CustomerNumber { get; set; } = string.Empty;

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; } = string.Empty;

        /// <summary>
        /// 客户类型（个人/企业）
        /// </summary>
        public string CustomerType { get; set; } = string.Empty;

        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string? PersonInChargeName { get; set; }

        /// <summary>
        /// 负责人手机号
        /// </summary>
        public string? PersonInChargePhone { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        public string? IdCard { get; set; }

        /// <summary>
        /// 所在地
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// 所属行业
        /// </summary>
        public string? Industry { get; set; }

        /// <summary>
        /// 统一社会信用代码（税号）
        /// </summary>
        public string? UnifiedSocialCreditCode { get; set; }

        /// <summary>
        /// 助记码
        /// </summary>
        public string? MnemonicCode { get; set; }

        /// <summary>
        /// 客户阶段
        /// </summary>
        public string? CustomerStage { get; set; }

        /// <summary>
        /// 客户自定义标签
        /// </summary>
        public string? CustomTags { get; set; }

        /// <summary>
        /// 上级客户ID
        /// </summary>
        public long? SuperiorCustomerId { get; set; }

        /// <summary>
        /// 上级客户
        /// </summary>
        public virtual Customer? SuperiorCustomer { get; set; }

        /// <summary>
        /// 使用状态（启用/停用）
        /// </summary>
        public string UsageStatus { get; set; } = "启用";

        /// <summary>
        /// 客户级别
        /// </summary>
        public string? CustomerLevel { get; set; }

        /// <summary>
        /// 客户集团分类
        /// </summary>
        public string? CustomerGroupClassification { get; set; }

        /// <summary>
        /// 录入单位
        /// </summary>
        public string? EntryUnit { get; set; }

        /// <summary>
        /// 使用单位
        /// </summary>
        public string? UsageUnit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 归集档案
        /// </summary>
        public bool IsArchived { get; set; } = false;

        /// <summary>
        /// 地图定位经度
        /// </summary>
        public decimal? Longitude { get; set; }

        /// <summary>
        /// 地图定位纬度
        /// </summary>
        public decimal? Latitude { get; set; }

        // 导航属性
        /// <summary>
        /// 银行账户列表
        /// </summary>
        public virtual ICollection<CustomerBankAccount> BankAccounts { get; set; } = new List<CustomerBankAccount>();

        /// <summary>
        /// 服务人员列表
        /// </summary>
        public virtual ICollection<CustomerServicePerson> ServicePersons { get; set; } = new List<CustomerServicePerson>();

        /// <summary>
        /// 联系信息列表
        /// </summary>
        public virtual ICollection<CustomerContact> Contacts { get; set; } = new List<CustomerContact>();

        /// <summary>
        /// 产品方案列表
        /// </summary>
        public virtual ICollection<CustomerProductSolution> ProductSolutions { get; set; } = new List<CustomerProductSolution>();

        /// <summary>
        /// 折扣方案列表
        /// </summary>
        public virtual ICollection<CustomerDiscountSolution> DiscountSolutions { get; set; } = new List<CustomerDiscountSolution>();

        /// <summary>
        /// 附件列表
        /// </summary>
        public virtual ICollection<CustomerAttachment> Attachments { get; set; } = new List<CustomerAttachment>();

        /// <summary>
        /// 关联账户
        /// </summary>
        public virtual CustomerAccount? AssociatedAccount { get; set; }
    }
} 